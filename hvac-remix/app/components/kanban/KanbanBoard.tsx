/**
 * Advanced Kanban Board Component
 * 8-Stage HVAC Workflow with Pragmatic DnD
 * Part of FAZA 2: Workflow & Kanban Enhancement
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import { 
  Clock, 
  User, 
  MapPin, 
  AlertCircle, 
  CheckCircle, 
  Package, 
  Wrench,
  FileText,
  TrendingUp,
  Filter,
  Plus
} from 'lucide-react';

// Pragmatic DnD imports
import { DragDropContext, Droppable, Draggable } from '@atlaskit/pragmatic-drag-and-drop-react-beautiful-dnd';
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine';
import { 
  draggable, 
  dropTargetForElements,
  monitorForElements 
} from '@atlaskit/pragmatic-drag-and-drop/element/adapter';

import type { KanbanBoard as KanbanBoardType, KanbanCard, KanbanStage } from '~/services/advanced-kanban.server';

interface KanbanBoardProps {
  initialBoard?: KanbanBoardType;
  filters?: {
    technicianId?: string;
    customerId?: string;
    priority?: string;
    serviceType?: string;
  };
  onCardMove?: (cardId: string, fromStage: KanbanStage, toStage: KanbanStage) => Promise<void>;
  onCardClick?: (card: KanbanCard) => void;
}

export function KanbanBoard({ 
  initialBoard, 
  filters, 
  onCardMove, 
  onCardClick 
}: KanbanBoardProps) {
  const [board, setBoard] = useState<KanbanBoardType | null>(initialBoard || null);
  const [loading, setLoading] = useState(false);
  const [draggedCard, setDraggedCard] = useState<string | null>(null);

  // Load board data
  const loadBoard = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        action: 'get-board',
        ...filters,
      });

      const response = await fetch(`/api/kanban?${params}`);
      const data = await response.json();
      
      if (data.board) {
        setBoard(data.board);
      }
    } catch (error) {
      console.error('Error loading kanban board:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!initialBoard) {
      loadBoard();
    }
  }, [filters]);

  // Handle drag end
  const handleDragEnd = async (result: any) => {
    const { destination, source, draggableId } = result;

    if (!destination) {
      setDraggedCard(null);
      return;
    }

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      setDraggedCard(null);
      return;
    }

    const fromStage = source.droppableId as KanbanStage;
    const toStage = destination.droppableId as KanbanStage;

    // Optimistic update
    if (board) {
      const newBoard = { ...board };
      const sourceCards = [...newBoard.stages[fromStage].cards];
      const destCards = fromStage === toStage ? sourceCards : [...newBoard.stages[toStage].cards];

      const [movedCard] = sourceCards.splice(source.index, 1);
      movedCard.stage = toStage;
      destCards.splice(destination.index, 0, movedCard);

      newBoard.stages[fromStage].cards = sourceCards;
      newBoard.stages[toStage].cards = destCards;

      setBoard(newBoard);
    }

    // Call API
    try {
      if (onCardMove) {
        await onCardMove(draggableId, fromStage, toStage);
      } else {
        const formData = new FormData();
        formData.append('action', 'move-card');
        formData.append('cardId', draggableId);
        formData.append('fromStage', fromStage);
        formData.append('toStage', toStage);

        await fetch('/api/kanban', {
          method: 'POST',
          body: formData,
        });
      }
    } catch (error) {
      console.error('Error moving card:', error);
      // Revert optimistic update
      loadBoard();
    }

    setDraggedCard(null);
  };

  if (!board) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading kanban board...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Board metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Total Cards</p>
                <p className="text-2xl font-bold">{board.metrics.totalCards}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium">Avg Cycle Time</p>
                <p className="text-2xl font-bold">{board.metrics.averageCycleTime}d</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Efficiency</p>
                <p className="text-2xl font-bold">{board.metrics.efficiency}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium">Bottlenecks</p>
                <p className="text-sm font-bold">{board.metrics.bottlenecks.join(', ') || 'None'}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Kanban board */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="flex space-x-4 overflow-x-auto pb-4">
          {Object.entries(board.stages).map(([stageKey, stage]) => (
            <KanbanColumn
              key={stageKey}
              stageKey={stageKey as KanbanStage}
              stage={stage}
              onCardClick={onCardClick}
              isDraggedOver={draggedCard !== null}
            />
          ))}
        </div>
      </DragDropContext>
    </div>
  );
}

// Kanban Column Component
interface KanbanColumnProps {
  stageKey: KanbanStage;
  stage: any;
  onCardClick?: (card: KanbanCard) => void;
  isDraggedOver: boolean;
}

function KanbanColumn({ stageKey, stage, onCardClick, isDraggedOver }: KanbanColumnProps) {
  const getStageIcon = (stage: KanbanStage) => {
    switch (stage) {
      case 'BACKLOG': return <FileText className="h-4 w-4" />;
      case 'READY': return <Clock className="h-4 w-4" />;
      case 'SCHEDULED': return <User className="h-4 w-4" />;
      case 'IN_PROGRESS': return <Wrench className="h-4 w-4" />;
      case 'WAITING_PARTS': return <Package className="h-4 w-4" />;
      case 'QUALITY_CHECK': return <CheckCircle className="h-4 w-4" />;
      case 'DONE': return <CheckCircle className="h-4 w-4" />;
      case 'INVOICED': return <FileText className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <div className="flex-shrink-0 w-80">
      <Card className="h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: stage.color }}
              />
              {getStageIcon(stageKey)}
              <CardTitle className="text-sm font-medium">{stage.name}</CardTitle>
            </div>
            <Badge variant="outline" className="text-xs">
              {stage.cards.length}
            </Badge>
          </div>
          <p className="text-xs text-gray-600">{stage.description}</p>
          
          {stage.limits?.max && (
            <div className="flex items-center space-x-2 text-xs">
              <span className="text-gray-500">WIP Limit:</span>
              <Badge 
                variant={stage.cards.length >= stage.limits.max ? "destructive" : "outline"}
                className="text-xs"
              >
                {stage.cards.length}/{stage.limits.max}
              </Badge>
            </div>
          )}
        </CardHeader>

        <Droppable droppableId={stageKey}>
          {(provided, snapshot) => (
            <CardContent
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={`space-y-3 min-h-96 max-h-96 overflow-y-auto ${
                snapshot.isDraggedOver ? 'bg-blue-50' : ''
              }`}
            >
              {stage.cards.map((card: KanbanCard, index: number) => (
                <KanbanCardComponent
                  key={card.id}
                  card={card}
                  index={index}
                  onClick={() => onCardClick?.(card)}
                />
              ))}
              {provided.placeholder}
              
              {stage.cards.length === 0 && (
                <div className="text-center py-8 text-gray-400">
                  <div className="text-4xl mb-2">📋</div>
                  <p className="text-sm">No cards in this stage</p>
                </div>
              )}
            </CardContent>
          )}
        </Droppable>
      </Card>
    </div>
  );
}

// Kanban Card Component
interface KanbanCardComponentProps {
  card: KanbanCard;
  index: number;
  onClick?: () => void;
}

function KanbanCardComponent({ card, index, onClick }: KanbanCardComponentProps) {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'destructive';
      case 'HIGH': return 'secondary';
      case 'MEDIUM': return 'outline';
      case 'LOW': return 'outline';
      default: return 'outline';
    }
  };

  const getServiceTypeIcon = (serviceType: string) => {
    switch (serviceType) {
      case 'INSTALLATION': return '🔧';
      case 'REPAIR': return '⚡';
      case 'MAINTENANCE': return '🛠️';
      case 'INSPECTION': return '🔍';
      default: return '📋';
    }
  };

  return (
    <Draggable draggableId={card.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`bg-white border rounded-lg p-3 cursor-pointer hover:shadow-md transition-shadow ${
            snapshot.isDragging ? 'shadow-lg rotate-2' : ''
          }`}
          onClick={onClick}
        >
          <div className="space-y-2">
            {/* Header */}
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-lg">{getServiceTypeIcon(card.serviceType)}</span>
                <Badge variant={getPriorityColor(card.priority)} className="text-xs">
                  {card.priority}
                </Badge>
              </div>
              <span className="text-xs text-gray-500">
                #{card.id.slice(-6)}
              </span>
            </div>

            {/* Title */}
            <h4 className="font-medium text-sm line-clamp-2">{card.title}</h4>

            {/* Customer */}
            <div className="flex items-center space-x-2 text-xs text-gray-600">
              <User className="h-3 w-3" />
              <span className="truncate">{card.customer.name}</span>
            </div>

            {/* Location */}
            {card.customer.address && (
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <MapPin className="h-3 w-3" />
                <span className="truncate">{card.customer.address}</span>
              </div>
            )}

            {/* Technician */}
            {card.technician && (
              <div className="flex items-center space-x-2">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={`/avatars/${card.technician.id}.jpg`} />
                  <AvatarFallback className="text-xs">
                    {card.technician.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <span className="text-xs text-gray-600 truncate">
                  {card.technician.name}
                </span>
                <div 
                  className={`w-2 h-2 rounded-full ${
                    card.technician.availability === 'AVAILABLE' ? 'bg-green-500' :
                    card.technician.availability === 'BUSY' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                />
              </div>
            )}

            {/* Scheduled date */}
            {card.scheduledDate && (
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <Clock className="h-3 w-3" />
                <span>{new Date(card.scheduledDate).toLocaleDateString()}</span>
              </div>
            )}

            {/* Equipment type */}
            {card.metadata.equipmentType && (
              <div className="text-xs text-gray-600">
                Equipment: {card.metadata.equipmentType}
              </div>
            )}

            {/* Footer */}
            <div className="flex items-center justify-between pt-2 border-t">
              <span className="text-xs text-gray-500">
                {card.estimatedDuration}h est.
              </span>
              <span className="text-xs text-gray-500">
                {new Date(card.createdAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      )}
    </Draggable>
  );
}

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "~/components/ui/tabs";
import { Progress } from "~/components/ui/progress";
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  LineChart, Line, PieChart, Pie, Cell, AreaChart, Area
} from 'recharts';
import { 
  Mail, Phone, TrendingUp, TrendingDown, AlertTriangle, CheckCircle,
  Clock, Users, DollarSign, MessageSquare, Brain, Zap
} from 'lucide-react';

// 📧 Dolores Email Intelligence Analytics Dashboard
// Comprehensive BI dashboard for email and transcription analysis

interface DoloresAnalytics {
  totalEmails: number;
  transcriptionCount: number;
  averageScore: number;
  topCategories: string[];
  trendData: TrendPoint[];
  sentimentDistribution: SentimentData[];
  priorityBreakdown: PriorityData[];
  customerEnrichment: EnrichmentStats;
  workflowTriggers: WorkflowStats;
  businessInsights: BusinessMetrics;
}

interface TrendPoint {
  date: string;
  emails: number;
  transcriptions: number;
  avgSentiment: number;
}

interface SentimentData {
  sentiment: string;
  count: number;
  percentage: number;
}

interface PriorityData {
  priority: string;
  count: number;
  responseTime: string;
}

interface EnrichmentStats {
  profilesEnriched: number;
  newContacts: number;
  updatedPreferences: number;
  churnRiskIdentified: number;
}

interface WorkflowStats {
  triggersActivated: number;
  ticketsCreated: number;
  escalationsTriggered: number;
  followUpsScheduled: number;
}

interface BusinessMetrics {
  leadScore: number;
  conversionProbability: number;
  estimatedRevenue: number;
  customerSatisfaction: number;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export function DoloresAnalyticsDashboard() {
  const [analytics, setAnalytics] = useState<DoloresAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('7d');

  useEffect(() => {
    fetchAnalytics();
  }, [timeframe]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/dolores/analytics?timeframe=${timeframe}`);
      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to fetch Dolores analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No analytics data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">📧 Dolores Email Intelligence</h1>
          <p className="text-gray-600">Comprehensive email and transcription analysis dashboard</p>
        </div>
        <div className="flex gap-2">
          {['24h', '7d', '30d', '90d'].map((period) => (
            <Button
              key={period}
              variant={timeframe === period ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeframe(period)}
            >
              {period}
            </Button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Emails</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalEmails.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transcriptions</CardTitle>
            <Phone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.transcriptionCount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {((analytics.transcriptionCount / analytics.totalEmails) * 100).toFixed(1)}% of emails
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Sentiment</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.averageScore.toFixed(1)}</div>
            <Progress value={analytics.averageScore * 10} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue Impact</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${analytics.businessInsights.estimatedRevenue.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Estimated from analysis
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sentiment">Sentiment Analysis</TabsTrigger>
          <TabsTrigger value="enrichment">Customer Enrichment</TabsTrigger>
          <TabsTrigger value="workflows">Workflow Automation</TabsTrigger>
          <TabsTrigger value="business">Business Intelligence</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Email Trends */}
            <Card>
              <CardHeader>
                <CardTitle>📈 Email Processing Trends</CardTitle>
                <CardDescription>Email volume and transcription analysis over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={analytics.trendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area type="monotone" dataKey="emails" stackId="1" stroke="#8884d8" fill="#8884d8" />
                    <Area type="monotone" dataKey="transcriptions" stackId="1" stroke="#82ca9d" fill="#82ca9d" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Priority Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>⚡ Priority Distribution</CardTitle>
                <CardDescription>Email priority levels and response times</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.priorityBreakdown.map((item, index) => (
                    <div key={item.priority} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant={
                          item.priority === 'urgent' ? 'destructive' :
                          item.priority === 'high' ? 'default' :
                          item.priority === 'medium' ? 'secondary' : 'outline'
                        }>
                          {item.priority}
                        </Badge>
                        <span className="text-sm">{item.count} emails</span>
                      </div>
                      <div className="text-sm text-gray-500">
                        Avg: {item.responseTime}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sentiment" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Sentiment Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>💭 Sentiment Distribution</CardTitle>
                <CardDescription>Customer sentiment analysis from emails and transcriptions</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={analytics.sentimentDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ sentiment, percentage }) => `${sentiment} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {analytics.sentimentDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Sentiment Trends */}
            <Card>
              <CardHeader>
                <CardTitle>📊 Sentiment Trends</CardTitle>
                <CardDescription>Average sentiment score over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={analytics.trendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={[-1, 1]} />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="avgSentiment" stroke="#8884d8" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="enrichment" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Profiles Enriched</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.customerEnrichment.profilesEnriched}</div>
                <p className="text-xs text-muted-foreground">Customer profiles updated</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">New Contacts</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.customerEnrichment.newContacts}</div>
                <p className="text-xs text-muted-foreground">Automatically created</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Preferences Updated</CardTitle>
                <Brain className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.customerEnrichment.updatedPreferences}</div>
                <p className="text-xs text-muted-foreground">Communication preferences</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Churn Risk Identified</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.customerEnrichment.churnRiskIdentified}</div>
                <p className="text-xs text-muted-foreground">High-risk customers</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="workflows" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Triggers Activated</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.workflowTriggers.triggersActivated}</div>
                <p className="text-xs text-muted-foreground">Automated workflows</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tickets Created</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.workflowTriggers.ticketsCreated}</div>
                <p className="text-xs text-muted-foreground">Service tickets</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Escalations</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.workflowTriggers.escalationsTriggered}</div>
                <p className="text-xs text-muted-foreground">Manager escalations</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Follow-ups</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.workflowTriggers.followUpsScheduled}</div>
                <p className="text-xs text-muted-foreground">Scheduled follow-ups</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="business" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>💼 Business Intelligence Metrics</CardTitle>
                <CardDescription>Key business insights from email analysis</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Lead Score</span>
                  <span className="text-2xl font-bold">{analytics.businessInsights.leadScore}/100</span>
                </div>
                <Progress value={analytics.businessInsights.leadScore} />

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Conversion Probability</span>
                  <span className="text-2xl font-bold">{(analytics.businessInsights.conversionProbability * 100).toFixed(1)}%</span>
                </div>
                <Progress value={analytics.businessInsights.conversionProbability * 100} />

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Customer Satisfaction</span>
                  <span className="text-2xl font-bold">{analytics.businessInsights.customerSatisfaction.toFixed(1)}/10</span>
                </div>
                <Progress value={analytics.businessInsights.customerSatisfaction * 10} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>📊 Top Categories</CardTitle>
                <CardDescription>Most common email categories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {analytics.topCategories.map((category, index) => (
                    <div key={category} className="flex items-center justify-between">
                      <Badge variant="outline">{category}</Badge>
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${100 - (index * 20)}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "~/components/ui/tabs";
import { Progress } from "~/components/ui/progress";
import { 
  Upload, Download, Search, Filter, Trash2, Eye, FileText, 
  Image, Archive, Clock, Database, Cloud, HardDrive
} from 'lucide-react';
import { useDropzone } from 'react-dropzone';

// 📁 File Manager Dashboard for Dolores Email Intelligence
// Comprehensive file management with hybrid storage visualization

interface FileInfo {
  id: number;
  originalName: string;
  contentType: string;
  size: number;
  storageType: string;
  storageLocation: string;
  extractedText?: string;
  processingStatus: string;
  accessLevel: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

interface StorageStats {
  totalFiles: number;
  totalSize: number;
  databaseFiles: number;
  databaseSize: number;
  minioFiles: number;
  minioSize: number;
}

interface UploadProgress {
  fileId?: number;
  fileName: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

export function FileManagerDashboard() {
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [storageStats, setStorageStats] = useState<StorageStats | null>(null);
  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<number[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFiles();
    fetchStorageStats();
  }, []);

  const fetchFiles = async () => {
    try {
      const response = await fetch('/api/files');
      const data = await response.json();
      setFiles(data.files || []);
    } catch (error) {
      console.error('Failed to fetch files:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStorageStats = async () => {
    try {
      const response = await fetch('/api/files/stats');
      const data = await response.json();
      setStorageStats(data);
    } catch (error) {
      console.error('Failed to fetch storage stats:', error);
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    for (const file of acceptedFiles) {
      const uploadProgress: UploadProgress = {
        fileName: file.name,
        progress: 0,
        status: 'uploading',
      };
      
      setUploads(prev => [...prev, uploadProgress]);
      
      try {
        await uploadFile(file, uploadProgress);
      } catch (error) {
        console.error('Upload failed:', error);
        setUploads(prev => prev.map(u => 
          u.fileName === file.name 
            ? { ...u, status: 'error', error: 'Upload failed' }
            : u
        ));
      }
    }
  }, []);

  const uploadFile = async (file: File, uploadProgress: UploadProgress) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('extractContent', 'true');
    formData.append('priority', 'normal');

    try {
      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const result = await response.json();
      
      setUploads(prev => prev.map(u => 
        u.fileName === file.name 
          ? { 
              ...u, 
              fileId: result.fileId,
              progress: 100, 
              status: result.processingJobId ? 'processing' : 'completed' 
            }
          : u
      ));

      // If there's a processing job, monitor it
      if (result.processingJobId) {
        monitorProcessingJob(result.processingJobId, uploadProgress);
      }

      // Refresh file list
      fetchFiles();
      fetchStorageStats();

    } catch (error) {
      throw error;
    }
  };

  const monitorProcessingJob = async (jobId: number, uploadProgress: UploadProgress) => {
    const checkStatus = async () => {
      try {
        const response = await fetch(`/api/files/upload?jobId=${jobId}`);
        const jobStatus = await response.json();

        setUploads(prev => prev.map(u => 
          u.fileName === uploadProgress.fileName 
            ? { 
                ...u, 
                progress: jobStatus.progress || 100,
                status: jobStatus.status === 'completed' ? 'completed' : 'processing'
              }
            : u
        ));

        if (jobStatus.status === 'completed' || jobStatus.status === 'failed') {
          fetchFiles(); // Refresh file list
          return;
        }

        // Continue monitoring
        setTimeout(checkStatus, 2000);
      } catch (error) {
        console.error('Failed to check job status:', error);
      }
    };

    checkStatus();
  };

  const deleteFile = async (fileId: number) => {
    try {
      const response = await fetch(`/api/files/${fileId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setFiles(prev => prev.filter(f => f.id !== fileId));
        fetchStorageStats();
      }
    } catch (error) {
      console.error('Failed to delete file:', error);
    }
  };

  const downloadFile = (fileId: number, fileName: string) => {
    const link = document.createElement('a');
    link.href = `/api/files/${fileId}?action=download`;
    link.download = fileName;
    link.click();
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/plain': ['.txt'],
      'image/*': ['.jpg', '.jpeg', '.png', '.gif'],
    },
    maxSize: 100 * 1024 * 1024, // 100MB
  });

  const filteredFiles = files.filter(file =>
    file.originalName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    file.extractedText?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith('image/')) return <Image className="h-4 w-4" />;
    if (contentType.includes('pdf')) return <FileText className="h-4 w-4" />;
    if (contentType.includes('word') || contentType.includes('document')) return <FileText className="h-4 w-4" />;
    if (contentType.includes('sheet') || contentType.includes('excel')) return <Archive className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const getStorageIcon = (storageType: string) => {
    switch (storageType) {
      case 'database': return <Database className="h-4 w-4" />;
      case 'minio': return <Cloud className="h-4 w-4" />;
      case 'local': return <HardDrive className="h-4 w-4" />;
      default: return <Archive className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">📁 File Manager</h1>
          <p className="text-gray-600">Hybrid storage with intelligent content extraction</p>
        </div>
      </div>

      {/* Storage Statistics */}
      {storageStats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Files</CardTitle>
              <Archive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{storageStats.totalFiles.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {formatFileSize(storageStats.totalSize)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Database Storage</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{storageStats.databaseFiles.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {formatFileSize(storageStats.databaseSize)} • Small files
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">MinIO Storage</CardTitle>
              <Cloud className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{storageStats.minioFiles.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {formatFileSize(storageStats.minioSize)} • Large files
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle>📤 Upload Files</CardTitle>
          <CardDescription>
            Drag and drop files or click to browse. Supports PDF, DOCX, XLSX, TXT, and images.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            {isDragActive ? (
              <p className="text-blue-600">Drop the files here...</p>
            ) : (
              <div>
                <p className="text-gray-600 mb-2">Drag files here or click to browse</p>
                <p className="text-sm text-gray-500">Maximum file size: 100MB</p>
              </div>
            )}
          </div>

          {/* Upload Progress */}
          {uploads.length > 0 && (
            <div className="mt-4 space-y-2">
              {uploads.map((upload, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="flex-1">
                    <div className="flex justify-between text-sm">
                      <span>{upload.fileName}</span>
                      <span className="text-gray-500">{upload.progress}%</span>
                    </div>
                    <Progress value={upload.progress} className="mt-1" />
                  </div>
                  <Badge variant={
                    upload.status === 'completed' ? 'default' :
                    upload.status === 'error' ? 'destructive' :
                    'secondary'
                  }>
                    {upload.status}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search files by name or content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <Button variant="outline">
          <Filter className="h-4 w-4 mr-2" />
          Filters
        </Button>
      </div>

      {/* Files List */}
      <Card>
        <CardHeader>
          <CardTitle>📋 Files ({filteredFiles.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {filteredFiles.map((file) => (
              <div key={file.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-3">
                  {getFileIcon(file.contentType)}
                  <div>
                    <div className="font-medium">{file.originalName}</div>
                    <div className="text-sm text-gray-500 flex items-center space-x-2">
                      <span>{formatFileSize(file.size)}</span>
                      <span>•</span>
                      <div className="flex items-center space-x-1">
                        {getStorageIcon(file.storageType)}
                        <span>{file.storageType}</span>
                      </div>
                      <span>•</span>
                      <Badge variant={
                        file.processingStatus === 'completed' ? 'default' :
                        file.processingStatus === 'processing' ? 'secondary' :
                        file.processingStatus === 'failed' ? 'destructive' :
                        'outline'
                      }>
                        {file.processingStatus}
                      </Badge>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => downloadFile(file.id, file.originalName)}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {/* TODO: Open file details */}}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteFile(file.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

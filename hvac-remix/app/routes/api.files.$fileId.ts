import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { requireAuth } from "~/utils/auth.server";

// 📁 File Management API for Dolores Email Intelligence
// Handles file download, metadata, and management operations

interface FileInfo {
  id: number;
  originalName: string;
  contentType: string;
  size: number;
  storageType: string;
  storageLocation: string;
  checksumMD5: string;
  checksumSHA256: string;
  extractedText?: string;
  extractionMeta?: any;
  processingStatus: string;
  accessLevel: string;
  tags: string[];
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  accessedAt?: string;
  expiresAt?: string;
}

interface FileDownloadResponse {
  success: boolean;
  downloadUrl?: string;
  fileInfo?: FileInfo;
  error?: string;
}

// GET - Download file or get file info
export async function loader({ request, params }: LoaderFunctionArgs) {
  await requireAuth(request);

  const fileId = params.fileId;
  if (!fileId) {
    return json({ error: "File ID required" }, { status: 400 });
  }

  const url = new URL(request.url);
  const action = url.searchParams.get("action") || "info";

  try {
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";

    switch (action) {
      case "download":
        return await handleFileDownload(fileId, backendUrl);
      
      case "info":
        return await handleFileInfo(fileId, backendUrl);
      
      case "presigned":
        return await handlePresignedUrl(fileId, backendUrl, url.searchParams);
      
      default:
        return json({ error: "Invalid action" }, { status: 400 });
    }

  } catch (error) {
    console.error("File operation failed:", error);
    return json(
      { error: "File operation failed" },
      { status: 500 }
    );
  }
}

// DELETE - Delete file
export async function action({ request, params }: ActionFunctionArgs) {
  await requireAuth(request);

  const fileId = params.fileId;
  if (!fileId) {
    return json({ error: "File ID required" }, { status: 400 });
  }

  if (request.method !== "DELETE") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    const response = await fetch(`${backendUrl}/api/v1/files/${fileId}`, {
      method: "DELETE",
      headers: {
        "Authorization": `Bearer ${process.env.API_TOKEN}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Delete failed: ${response.status} ${errorText}`);
    }

    return json({ success: true, message: "File deleted successfully" });

  } catch (error) {
    console.error("File deletion failed:", error);
    return json(
      { error: "File deletion failed" },
      { status: 500 }
    );
  }
}

async function handleFileDownload(fileId: string, backendUrl: string): Promise<Response> {
  const response = await fetch(`${backendUrl}/api/v1/files/${fileId}/download`, {
    headers: {
      "Authorization": `Bearer ${process.env.API_TOKEN}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Download failed: ${response.status}`);
  }

  // Stream the file directly to the client
  const headers = new Headers();
  
  // Copy relevant headers from backend response
  const contentType = response.headers.get("content-type");
  const contentLength = response.headers.get("content-length");
  const contentDisposition = response.headers.get("content-disposition");
  
  if (contentType) headers.set("content-type", contentType);
  if (contentLength) headers.set("content-length", contentLength);
  if (contentDisposition) headers.set("content-disposition", contentDisposition);
  
  // Add cache headers
  headers.set("cache-control", "private, max-age=3600");
  
  return new Response(response.body, {
    status: 200,
    headers,
  });
}

async function handleFileInfo(fileId: string, backendUrl: string): Promise<Response> {
  const response = await fetch(`${backendUrl}/api/v1/files/${fileId}`, {
    headers: {
      "Authorization": `Bearer ${process.env.API_TOKEN}`,
    },
  });

  if (!response.ok) {
    if (response.status === 404) {
      return json({ error: "File not found" }, { status: 404 });
    }
    throw new Error(`Failed to get file info: ${response.status}`);
  }

  const fileInfo = await response.json();
  
  return json<FileDownloadResponse>({
    success: true,
    fileInfo: {
      id: fileInfo.id,
      originalName: fileInfo.original_name,
      contentType: fileInfo.content_type,
      size: fileInfo.size,
      storageType: fileInfo.storage_type,
      storageLocation: fileInfo.storage_location,
      checksumMD5: fileInfo.checksum_md5,
      checksumSHA256: fileInfo.checksum_sha256,
      extractedText: fileInfo.extracted_text,
      extractionMeta: fileInfo.extraction_meta,
      processingStatus: fileInfo.processing_status,
      accessLevel: fileInfo.access_level,
      tags: fileInfo.tags || [],
      metadata: fileInfo.metadata || {},
      createdAt: fileInfo.created_at,
      updatedAt: fileInfo.updated_at,
      accessedAt: fileInfo.accessed_at,
      expiresAt: fileInfo.expires_at,
    },
  });
}

async function handlePresignedUrl(
  fileId: string, 
  backendUrl: string, 
  searchParams: URLSearchParams
): Promise<Response> {
  const expiration = searchParams.get("expiration") || "3600"; // 1 hour default
  
  const response = await fetch(
    `${backendUrl}/api/v1/files/${fileId}/presigned?expiration=${expiration}`,
    {
      headers: {
        "Authorization": `Bearer ${process.env.API_TOKEN}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to generate presigned URL: ${response.status}`);
  }

  const result = await response.json();
  
  return json<FileDownloadResponse>({
    success: true,
    downloadUrl: result.download_url,
  });
}

// PATCH - Update file metadata
export async function PATCH({ request, params }: ActionFunctionArgs) {
  await requireAuth(request);

  const fileId = params.fileId;
  if (!fileId) {
    return json({ error: "File ID required" }, { status: 400 });
  }

  try {
    const body = await request.json();
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    const response = await fetch(`${backendUrl}/api/v1/files/${fileId}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.API_TOKEN}`,
      },
      body: JSON.stringify({
        tags: body.tags,
        metadata: body.metadata,
        access_level: body.accessLevel,
        expires_at: body.expiresAt,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Update failed: ${response.status} ${errorText}`);
    }

    const updatedFile = await response.json();
    
    return json({
      success: true,
      fileInfo: {
        id: updatedFile.id,
        originalName: updatedFile.original_name,
        tags: updatedFile.tags || [],
        metadata: updatedFile.metadata || {},
        accessLevel: updatedFile.access_level,
        expiresAt: updatedFile.expires_at,
        updatedAt: updatedFile.updated_at,
      },
    });

  } catch (error) {
    console.error("File update failed:", error);
    return json(
      { error: "File update failed" },
      { status: 500 }
    );
  }
}

/**
 * Kanban API Routes
 * Provides endpoints for advanced kanban workflow management
 * Part of FAZA 2: Workflow & Kanban Enhancement
 */

import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from '@remix-run/node';
import { 
  advancedKanbanService,
  getKanbanBoard,
  moveKanbanCard,
  type KanbanStage,
} from '~/services/advanced-kanban.server';
import { requireUserId } from '~/session.server';

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  
  const url = new URL(request.url);
  const action = url.searchParams.get('action');

  try {
    switch (action) {
      case 'get-board': {
        const filters: any = {};
        
        const technicianId = url.searchParams.get('technicianId');
        const customerId = url.searchParams.get('customerId');
        const priority = url.searchParams.get('priority');
        const serviceType = url.searchParams.get('serviceType');
        const startDate = url.searchParams.get('startDate');
        const endDate = url.searchParams.get('endDate');

        if (technicianId) filters.technicianId = technicianId;
        if (customerId) filters.customerId = customerId;
        if (priority) filters.priority = priority;
        if (serviceType) filters.serviceType = serviceType;
        
        if (startDate && endDate) {
          filters.dateRange = {
            start: new Date(startDate),
            end: new Date(endDate),
          };
        }

        const board = await getKanbanBoard(filters);
        return json({ board });
      }

      case 'get-metrics': {
        const board = await getKanbanBoard();
        return json({ metrics: board.metrics });
      }

      case 'get-stage-cards': {
        const stage = url.searchParams.get('stage') as KanbanStage;
        if (!stage) {
          return json({ error: 'Stage parameter required' }, { status: 400 });
        }

        const board = await getKanbanBoard();
        const stageCards = board.stages[stage]?.cards || [];
        
        return json({ cards: stageCards });
      }

      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Kanban API loader error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);

  const formData = await request.formData();
  const action = formData.get('action') as string;

  try {
    switch (action) {
      case 'move-card': {
        const cardId = formData.get('cardId') as string;
        const fromStage = formData.get('fromStage') as KanbanStage;
        const toStage = formData.get('toStage') as KanbanStage;
        const metadata = formData.get('metadata') ? 
          JSON.parse(formData.get('metadata') as string) : undefined;

        if (!cardId || !fromStage || !toStage) {
          return json({ error: 'Missing required fields' }, { status: 400 });
        }

        const result = await moveKanbanCard(cardId, fromStage, toStage, userId, metadata);
        return json({ result });
      }

      case 'update-card': {
        const cardId = formData.get('cardId') as string;
        const updates = JSON.parse(formData.get('updates') as string);

        if (!cardId || !updates) {
          return json({ error: 'Missing required fields' }, { status: 400 });
        }

        // Update service order
        await prisma.serviceOrder.update({
          where: { id: cardId },
          data: {
            ...updates,
            updatedAt: new Date(),
          },
        });

        return json({ success: true });
      }

      case 'assign-technician': {
        const cardId = formData.get('cardId') as string;
        const technicianId = formData.get('technicianId') as string;

        if (!cardId || !technicianId) {
          return json({ error: 'Missing required fields' }, { status: 400 });
        }

        await prisma.serviceOrder.update({
          where: { id: cardId },
          data: {
            assignedTechnicianId: technicianId,
            updatedAt: new Date(),
          },
        });

        // Create notification for technician
        await prisma.notification.create({
          data: {
            userId: technicianId,
            type: 'TECHNICIAN_ASSIGNMENT',
            title: 'New Service Assignment',
            message: `You have been assigned to service order ${cardId}`,
            data: { serviceOrderId: cardId },
            read: false,
          },
        });

        return json({ success: true });
      }

      case 'schedule-card': {
        const cardId = formData.get('cardId') as string;
        const scheduledDate = formData.get('scheduledDate') as string;
        const technicianId = formData.get('technicianId') as string;

        if (!cardId || !scheduledDate) {
          return json({ error: 'Missing required fields' }, { status: 400 });
        }

        const updateData: any = {
          scheduledDate: new Date(scheduledDate),
          status: 'SCHEDULED',
          updatedAt: new Date(),
        };

        if (technicianId) {
          updateData.assignedTechnicianId = technicianId;
        }

        await prisma.serviceOrder.update({
          where: { id: cardId },
          data: updateData,
        });

        // Send customer confirmation
        const serviceOrder = await prisma.serviceOrder.findUnique({
          where: { id: cardId },
          include: { customer: true },
        });

        if (serviceOrder?.customer.email) {
          await sendCustomerCommunication({
            customerId: serviceOrder.customerId,
            userId,
            channel: 'EMAIL',
            subject: 'Service Appointment Scheduled',
            content: `Your service appointment has been scheduled for ${new Date(scheduledDate).toLocaleDateString()}. We will contact you with more details.`,
            direction: 'OUTBOUND',
          });
        }

        return json({ success: true });
      }

      case 'add-card-note': {
        const cardId = formData.get('cardId') as string;
        const note = formData.get('note') as string;
        const noteType = formData.get('noteType') as string || 'general';

        if (!cardId || !note) {
          return json({ error: 'Missing required fields' }, { status: 400 });
        }

        const serviceOrder = await prisma.serviceOrder.findUnique({
          where: { id: cardId },
          select: { metadata: true },
        });

        const currentMetadata = (serviceOrder?.metadata as any) || {};
        const notes = currentMetadata.notes || [];

        notes.push({
          id: Date.now().toString(),
          type: noteType,
          content: note,
          userId,
          timestamp: new Date().toISOString(),
        });

        await prisma.serviceOrder.update({
          where: { id: cardId },
          data: {
            metadata: {
              ...currentMetadata,
              notes,
            },
            updatedAt: new Date(),
          },
        });

        return json({ success: true });
      }

      case 'update-card-priority': {
        const cardId = formData.get('cardId') as string;
        const priority = formData.get('priority') as string;

        if (!cardId || !priority) {
          return json({ error: 'Missing required fields' }, { status: 400 });
        }

        await prisma.serviceOrder.update({
          where: { id: cardId },
          data: {
            priority,
            updatedAt: new Date(),
          },
        });

        // If priority is URGENT, create notification
        if (priority === 'URGENT') {
          await prisma.notification.create({
            data: {
              userId,
              type: 'URGENT_PRIORITY',
              title: 'Urgent Service Order',
              message: `Service order ${cardId} has been marked as urgent`,
              data: { serviceOrderId: cardId },
              read: false,
            },
          });
        }

        return json({ success: true });
      }

      case 'bulk-move-cards': {
        const cardIds = JSON.parse(formData.get('cardIds') as string);
        const toStage = formData.get('toStage') as KanbanStage;

        if (!cardIds || !Array.isArray(cardIds) || !toStage) {
          return json({ error: 'Missing required fields' }, { status: 400 });
        }

        const results = [];
        
        for (const cardId of cardIds) {
          try {
            const serviceOrder = await prisma.serviceOrder.findUnique({
              where: { id: cardId },
              select: { status: true },
            });

            if (serviceOrder) {
              const result = await moveKanbanCard(
                cardId, 
                serviceOrder.status as KanbanStage, 
                toStage, 
                userId,
                { bulkMove: true }
              );
              results.push({ cardId, success: result.success, message: result.message });
            }
          } catch (error) {
            results.push({ cardId, success: false, message: 'Failed to move card' });
          }
        }

        return json({ results });
      }

      case 'create-automation-rule': {
        const rule = JSON.parse(formData.get('rule') as string);

        if (!rule) {
          return json({ error: 'Rule data required' }, { status: 400 });
        }

        // Create automation rule in database
        const createdRule = await prisma.automationRule.create({
          data: {
            name: rule.name,
            trigger: rule.trigger,
            action: rule.action,
            active: rule.active || true,
            userId,
          },
        });

        return json({ ruleId: createdRule.id });
      }

      case 'get-workflow-analytics': {
        const startDate = formData.get('startDate') as string;
        const endDate = formData.get('endDate') as string;

        const analytics = await advancedKanbanService.getWorkflowAnalytics({
          start: startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          end: endDate ? new Date(endDate) : new Date(),
        });

        return json({ analytics });
      }

      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Kanban API action error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}

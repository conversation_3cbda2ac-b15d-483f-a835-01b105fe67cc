/**
 * FAZA 3A: Purchase Orders Management Dashboard
 * Enhanced inventory management with automated purchase order system
 */

import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, Form, Link } from "@remix-run/react";
import { useState } from "react";
import { 
  PlusIcon, 
  DocumentTextIcon, 
  CheckCircleIcon, 
  ClockIcon,
  ExclamationTriangleIcon,
  TruckIcon,
  EyeIcon
} from "@heroicons/react/24/outline";

import { requireUserId } from "~/session.server";
import { EnhancedInventoryService } from "~/services/enhanced-inventory.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);
  const url = new URL(request.url);
  
  const status = url.searchParams.get("status") || undefined;
  const supplierId = url.searchParams.get("supplierId") || undefined;
  
  try {
    const [purchaseOrders, recommendations] = await Promise.all([
      EnhancedInventoryService.getPurchaseOrders({
        status,
        supplierId,
        limit: 50
      }),
      EnhancedInventoryService.getInventoryOptimizationRecommendations()
    ]);

    return json({
      purchaseOrders,
      recommendations: recommendations.slice(0, 10), // Top 10 recommendations
      filters: { status, supplierId }
    });
  } catch (error) {
    console.error("Error loading purchase orders:", error);
    return json({
      purchaseOrders: [],
      recommendations: [],
      filters: { status, supplierId },
      error: "Failed to load purchase orders"
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  const action = formData.get("action") as string;

  try {
    switch (action) {
      case "generateAutomatedPOs": {
        const purchaseOrders = await EnhancedInventoryService.generateAutomatedPurchaseOrders(userId);
        return json({ 
          success: true, 
          message: `Generated ${purchaseOrders.length} purchase orders`,
          purchaseOrders 
        });
      }

      case "approvePO": {
        const poId = formData.get("poId") as string;
        await EnhancedInventoryService.approvePurchaseOrder(poId, userId);
        return json({ success: true, message: "Purchase order approved" });
      }

      default:
        return json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error: any) {
    console.error("Purchase order action error:", error);
    return json({ error: error.message }, { status: 500 });
  }
};

export default function PurchaseOrdersPage() {
  const { purchaseOrders, recommendations, filters } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [selectedTab, setSelectedTab] = useState<'orders' | 'recommendations'>('orders');

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      DRAFT: { color: 'bg-gray-100 text-gray-800', icon: DocumentTextIcon },
      PENDING: { color: 'bg-yellow-100 text-yellow-800', icon: ClockIcon },
      APPROVED: { color: 'bg-blue-100 text-blue-800', icon: CheckCircleIcon },
      ORDERED: { color: 'bg-purple-100 text-purple-800', icon: TruckIcon },
      RECEIVED: { color: 'bg-green-100 text-green-800', icon: CheckCircleIcon },
      CANCELLED: { color: 'bg-red-100 text-red-800', icon: ExclamationTriangleIcon }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.DRAFT;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3" />
        {status}
      </span>
    );
  };

  const getUrgencyBadge = (urgency: string) => {
    const urgencyConfig = {
      LOW: 'bg-green-100 text-green-800',
      NORMAL: 'bg-blue-100 text-blue-800',
      HIGH: 'bg-orange-100 text-orange-800',
      URGENT: 'bg-red-100 text-red-800',
      CRITICAL: 'bg-red-200 text-red-900'
    };

    return (
      <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${urgencyConfig[urgency as keyof typeof urgencyConfig] || urgencyConfig.NORMAL}`}>
        {urgency}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Purchase Orders</h1>
          <p className="text-gray-600">Manage purchase orders and inventory optimization</p>
        </div>
        <div className="flex gap-3">
          <Form method="post">
            <input type="hidden" name="action" value="generateAutomatedPOs" />
            <button
              type="submit"
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <DocumentTextIcon className="h-4 w-4" />
              Generate Auto POs
            </button>
          </Form>
          <Link
            to="/inventory/purchase-orders/new"
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <PlusIcon className="h-4 w-4" />
            New Purchase Order
          </Link>
        </div>
      </div>

      {/* Action Messages */}
      {actionData?.success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">{actionData.message}</p>
            </div>
          </div>
        </div>
      )}

      {actionData?.error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{actionData.error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setSelectedTab('orders')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'orders'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Purchase Orders ({purchaseOrders.length})
          </button>
          <button
            onClick={() => setSelectedTab('recommendations')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'recommendations'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            AI Recommendations ({recommendations.length})
          </button>
        </nav>
      </div>

      {/* Purchase Orders Tab */}
      {selectedTab === 'orders' && (
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order Number
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Supplier
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Urgency
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {purchaseOrders.map((po) => (
                  <tr key={po.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{po.orderNumber}</div>
                      <div className="text-sm text-gray-500">{po.items.length} items</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{po.supplier?.name}</div>
                      <div className="text-sm text-gray-500">{po.supplier?.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(po.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getUrgencyBadge(po.urgencyLevel)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${po.totalAmount.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(po.orderDate).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <Link
                          to={`/inventory/purchase-orders/${po.id}`}
                          className="text-blue-600 hover:text-blue-900 flex items-center gap-1"
                        >
                          <EyeIcon className="h-4 w-4" />
                          View
                        </Link>
                        {po.status === 'DRAFT' && (
                          <Form method="post" className="inline">
                            <input type="hidden" name="action" value="approvePO" />
                            <input type="hidden" name="poId" value={po.id} />
                            <button
                              type="submit"
                              className="text-green-600 hover:text-green-900 flex items-center gap-1"
                            >
                              <CheckCircleIcon className="h-4 w-4" />
                              Approve
                            </button>
                          </Form>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {purchaseOrders.length === 0 && (
            <div className="text-center py-12">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No purchase orders</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating a new purchase order or generating automated orders.
              </p>
            </div>
          )}
        </div>
      )}

      {/* AI Recommendations Tab */}
      {selectedTab === 'recommendations' && (
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              AI-Powered Inventory Optimization Recommendations
            </h3>
            <div className="space-y-4">
              {recommendations.map((rec) => (
                <div
                  key={rec.partId}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-medium text-gray-900">{rec.partName}</h4>
                        {getUrgencyBadge(rec.urgency)}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          rec.recommendedAction === 'ORDER_NOW' ? 'bg-red-100 text-red-800' :
                          rec.recommendedAction === 'ORDER_SOON' ? 'bg-orange-100 text-orange-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {rec.recommendedAction.replace('_', ' ')}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{rec.reasoning}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>Current Stock: {rec.currentStock}</span>
                        <span>Recommended Quantity: {rec.recommendedQuantity}</span>
                        <span>Risk Level: {rec.riskLevel}</span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <Link
                        to={`/inventory/purchase-orders/new?partId=${rec.partId}&quantity=${rec.recommendedQuantity}`}
                        className="inline-flex items-center gap-1 px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        <PlusIcon className="h-4 w-4" />
                        Create PO
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {recommendations.length === 0 && (
              <div className="text-center py-8">
                <CheckCircleIcon className="mx-auto h-12 w-12 text-green-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">All inventory optimized</h3>
                <p className="mt-1 text-sm text-gray-500">
                  No immediate recommendations. Your inventory levels look good!
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

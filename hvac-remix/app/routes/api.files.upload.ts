import { json, type ActionFunctionArgs } from "@remix-run/node";
import { requireAuth } from "~/utils/auth.server";

// 📤 File Upload API for Dolores Email Intelligence
// Handles file uploads with hybrid storage strategy

interface FileUploadRequest {
  emailId?: number;
  originalName: string;
  contentType: string;
  size: number;
  tags?: string[];
  metadata?: Record<string, string>;
  accessLevel?: "private" | "internal" | "public" | "restricted";
  expirationDays?: number;
  extractContent?: boolean;
  priority?: "low" | "normal" | "high" | "urgent";
}

interface FileUploadResponse {
  success: boolean;
  fileId?: number;
  storageLocation?: string;
  storageType?: string;
  checksumMD5?: string;
  checksumSHA256?: string;
  processingJobId?: number;
  error?: string;
}

export async function action({ request }: ActionFunctionArgs) {
  // Require authentication
  await requireAuth(request);

  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Parse multipart form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    
    if (!file) {
      return json<FileUploadResponse>(
        { success: false, error: "No file provided" },
        { status: 400 }
      );
    }

    // Parse additional metadata
    const uploadRequest: FileUploadRequest = {
      originalName: file.name,
      contentType: file.type,
      size: file.size,
      emailId: formData.get("emailId") ? Number(formData.get("emailId")) : undefined,
      tags: formData.get("tags") ? JSON.parse(formData.get("tags") as string) : [],
      metadata: formData.get("metadata") ? JSON.parse(formData.get("metadata") as string) : {},
      accessLevel: (formData.get("accessLevel") as any) || "private",
      expirationDays: formData.get("expirationDays") ? Number(formData.get("expirationDays")) : undefined,
      extractContent: formData.get("extractContent") === "true",
      priority: (formData.get("priority") as any) || "normal",
    };

    // Validate file size (100MB limit)
    const maxFileSize = 100 * 1024 * 1024; // 100MB
    if (uploadRequest.size > maxFileSize) {
      return json<FileUploadResponse>(
        { success: false, error: `File too large. Maximum size is ${maxFileSize / 1024 / 1024}MB` },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "text/plain",
      "image/jpeg",
      "image/png",
      "image/gif",
    ];

    if (!allowedTypes.includes(uploadRequest.contentType)) {
      return json<FileUploadResponse>(
        { success: false, error: "File type not supported" },
        { status: 400 }
      );
    }

    // Convert file to buffer for backend
    const fileBuffer = await file.arrayBuffer();
    const fileData = new Uint8Array(fileBuffer);

    // Call GoBackend-Kratos file upload API
    const uploadResult = await uploadFileToBackend(uploadRequest, fileData);

    if (!uploadResult.success) {
      return json<FileUploadResponse>(
        { success: false, error: uploadResult.error },
        { status: 500 }
      );
    }

    return json<FileUploadResponse>({
      success: true,
      fileId: uploadResult.fileId,
      storageLocation: uploadResult.storageLocation,
      storageType: uploadResult.storageType,
      checksumMD5: uploadResult.checksumMD5,
      checksumSHA256: uploadResult.checksumSHA256,
      processingJobId: uploadResult.processingJobId,
    });

  } catch (error) {
    console.error("File upload failed:", error);
    return json<FileUploadResponse>(
      { success: false, error: "Upload failed" },
      { status: 500 }
    );
  }
}

async function uploadFileToBackend(
  uploadRequest: FileUploadRequest,
  fileData: Uint8Array
): Promise<FileUploadResponse> {
  try {
    // TODO: Replace with actual GoBackend-Kratos API call
    // For now, simulate the upload process
    
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    // Create form data for backend
    const formData = new FormData();
    formData.append("file", new Blob([fileData], { type: uploadRequest.contentType }), uploadRequest.originalName);
    formData.append("email_id", uploadRequest.emailId?.toString() || "");
    formData.append("tags", JSON.stringify(uploadRequest.tags || []));
    formData.append("metadata", JSON.stringify(uploadRequest.metadata || {}));
    formData.append("access_level", uploadRequest.accessLevel || "private");
    formData.append("expiration_days", uploadRequest.expirationDays?.toString() || "");
    formData.append("extract_content", uploadRequest.extractContent?.toString() || "false");
    formData.append("priority", uploadRequest.priority || "normal");

    const response = await fetch(`${backendUrl}/api/v1/files/upload`, {
      method: "POST",
      body: formData,
      headers: {
        // Add authentication headers if needed
        "Authorization": `Bearer ${process.env.API_TOKEN}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Backend upload failed: ${response.status} ${errorText}`);
    }

    const result = await response.json();
    
    return {
      success: true,
      fileId: result.file_id,
      storageLocation: result.storage_location,
      storageType: result.storage_type,
      checksumMD5: result.checksum_md5,
      checksumSHA256: result.checksum_sha256,
      processingJobId: result.processing_job?.id,
    };

  } catch (error) {
    console.error("Backend upload error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// GET endpoint for upload status
export async function loader({ request }: ActionFunctionArgs) {
  await requireAuth(request);

  const url = new URL(request.url);
  const jobId = url.searchParams.get("jobId");

  if (!jobId) {
    return json({ error: "Job ID required" }, { status: 400 });
  }

  try {
    // TODO: Call backend to get job status
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    const response = await fetch(`${backendUrl}/api/v1/files/jobs/${jobId}`, {
      headers: {
        "Authorization": `Bearer ${process.env.API_TOKEN}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to get job status: ${response.status}`);
    }

    const jobStatus = await response.json();
    
    return json({
      jobId: jobStatus.id,
      status: jobStatus.status,
      progress: jobStatus.progress || 0,
      extractedText: jobStatus.extracted_text,
      metadata: jobStatus.extracted_meta,
      error: jobStatus.error_message,
      completedAt: jobStatus.completed_at,
    });

  } catch (error) {
    console.error("Failed to get job status:", error);
    return json(
      { error: "Failed to get job status" },
      { status: 500 }
    );
  }
}

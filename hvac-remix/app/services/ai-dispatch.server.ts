/**
 * AI-Powered Dispatch System
 * Intelligent technician assignment with location, skills, and availability optimization
 * Part of FAZA 2: Workflow & Kanban Enhancement
 */

import { prisma } from '~/db.server';
import { bielikService } from './bielik.server';
import { sendCustomerCommunication } from './communication.service';

export interface TechnicianProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  skills: string[];
  certifications: string[];
  experience: number; // years
  rating: number; // 1-5
  currentLocation?: {
    lat: number;
    lng: number;
    address?: string;
    lastUpdated: Date;
  };
  availability: {
    status: 'AVAILABLE' | 'BUSY' | 'OFFLINE' | 'ON_BREAK';
    schedule: {
      [key: string]: { // day of week
        start: string; // HH:mm
        end: string;   // HH:mm
        breaks?: Array<{ start: string; end: string }>;
      };
    };
    timeOff?: Array<{ start: Date; end: Date; reason: string }>;
  };
  workload: {
    currentTasks: number;
    maxCapacity: number;
    averageTaskDuration: number;
    completionRate: number;
  };
  preferences: {
    serviceTypes: string[];
    maxTravelDistance: number; // km
    preferredCustomers?: string[];
    avoidCustomers?: string[];
  };
  performance: {
    averageResponseTime: number; // minutes
    customerSatisfaction: number; // 1-5
    taskCompletionTime: number; // vs estimated
    qualityScore: number; // 1-5
  };
}

export interface ServiceRequest {
  id: string;
  customerId: string;
  serviceType: 'INSTALLATION' | 'REPAIR' | 'MAINTENANCE' | 'INSPECTION';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  equipmentType: string;
  location: {
    lat: number;
    lng: number;
    address: string;
  };
  estimatedDuration: number; // hours
  requiredSkills: string[];
  preferredDate?: Date;
  customerPreferences?: {
    preferredTechnician?: string;
    timeSlots?: string[];
    specialRequests?: string[];
  };
  complexity: 'SIMPLE' | 'MODERATE' | 'COMPLEX' | 'EXPERT';
  urgency: {
    level: number; // 1-10
    reason?: string;
    deadline?: Date;
  };
}

export interface DispatchRecommendation {
  technicianId: string;
  confidence: number; // 0-1
  score: number; // 0-100
  reasoning: string;
  factors: {
    skillMatch: number;
    locationProximity: number;
    availability: number;
    workload: number;
    performance: number;
    customerPreference: number;
  };
  estimatedArrival: Date;
  estimatedCompletion: Date;
  travelTime: number; // minutes
  alternatives?: DispatchRecommendation[];
}

export interface DispatchResult {
  success: boolean;
  assignedTechnician?: TechnicianProfile;
  recommendation: DispatchRecommendation;
  scheduledTime: Date;
  notifications: string[];
  fallbackOptions?: DispatchRecommendation[];
}

/**
 * AI-Powered Dispatch Service
 */
class AIDispatchService {
  private readonly SKILL_WEIGHT = 0.25;
  private readonly LOCATION_WEIGHT = 0.20;
  private readonly AVAILABILITY_WEIGHT = 0.20;
  private readonly WORKLOAD_WEIGHT = 0.15;
  private readonly PERFORMANCE_WEIGHT = 0.15;
  private readonly PREFERENCE_WEIGHT = 0.05;

  /**
   * Find optimal technician assignment using AI
   */
  async findOptimalAssignment(request: ServiceRequest): Promise<DispatchRecommendation[]> {
    try {
      // Get available technicians
      const technicians = await this.getAvailableTechnicians(request);

      if (technicians.length === 0) {
        throw new Error('No available technicians found');
      }

      // Calculate scores for each technician
      const recommendations: DispatchRecommendation[] = [];

      for (const technician of technicians) {
        const recommendation = await this.calculateTechnicianScore(technician, request);
        recommendations.push(recommendation);
      }

      // Sort by score (highest first)
      recommendations.sort((a, b) => b.score - a.score);

      // Use AI to refine recommendations
      const aiRefinedRecommendations = await this.refineWithAI(recommendations, request);

      return aiRefinedRecommendations;
    } catch (error) {
      console.error('Error finding optimal assignment:', error);
      throw new Error('Failed to find optimal technician assignment');
    }
  }

  /**
   * Automatically dispatch service request
   */
  async autoDispatch(serviceOrderId: string): Promise<DispatchResult> {
    try {
      // Get service order details
      const serviceOrder = await prisma.serviceOrder.findUnique({
        where: { id: serviceOrderId },
        include: {
          customer: true,
        },
      });

      if (!serviceOrder) {
        throw new Error('Service order not found');
      }

      // Convert to service request format
      const request = await this.convertToServiceRequest(serviceOrder);

      // Find optimal assignment
      const recommendations = await this.findOptimalAssignment(request);

      if (recommendations.length === 0) {
        throw new Error('No suitable technicians found');
      }

      const bestRecommendation = recommendations[0];

      // Assign technician
      const assignedTechnician = await this.assignTechnician(
        serviceOrderId,
        bestRecommendation.technicianId,
        bestRecommendation
      );

      // Send notifications
      const notifications = await this.sendDispatchNotifications(
        serviceOrder,
        assignedTechnician,
        bestRecommendation
      );

      return {
        success: true,
        assignedTechnician,
        recommendation: bestRecommendation,
        scheduledTime: bestRecommendation.estimatedArrival,
        notifications,
        fallbackOptions: recommendations.slice(1, 4), // Top 3 alternatives
      };
    } catch (error) {
      console.error('Error in auto dispatch:', error);
      return {
        success: false,
        recommendation: {} as DispatchRecommendation,
        scheduledTime: new Date(),
        notifications: [`Dispatch failed: ${error.message}`],
      };
    }
  }

  /**
   * Get available technicians based on request criteria
   */
  private async getAvailableTechnicians(request: ServiceRequest): Promise<TechnicianProfile[]> {
    const technicians = await prisma.user.findMany({
      where: {
        role: 'TECHNICIAN',
        metadata: {
          path: ['availability', 'status'],
          in: ['AVAILABLE', 'ON_BREAK'],
        },
      },
    });

    return technicians.map(tech => this.convertToTechnicianProfile(tech));
  }

  /**
   * Calculate technician score for service request
   */
  private async calculateTechnicianScore(
    technician: TechnicianProfile,
    request: ServiceRequest
  ): Promise<DispatchRecommendation> {
    // Calculate individual factor scores
    const skillMatch = this.calculateSkillMatch(technician.skills, request.requiredSkills);
    const locationProximity = await this.calculateLocationProximity(
      technician.currentLocation,
      request.location
    );
    const availability = this.calculateAvailability(technician, request.preferredDate);
    const workload = this.calculateWorkloadScore(technician.workload);
    const performance = this.calculatePerformanceScore(technician.performance);
    const customerPreference = this.calculateCustomerPreference(technician, request);

    // Calculate weighted total score
    const totalScore =
      (skillMatch * this.SKILL_WEIGHT) +
      (locationProximity * this.LOCATION_WEIGHT) +
      (availability * this.AVAILABILITY_WEIGHT) +
      (workload * this.WORKLOAD_WEIGHT) +
      (performance * this.PERFORMANCE_WEIGHT) +
      (customerPreference * this.PREFERENCE_WEIGHT);

    // Calculate travel time and estimated times
    const travelTime = await this.calculateTravelTime(
      technician.currentLocation,
      request.location
    );

    const estimatedArrival = new Date(Date.now() + travelTime * 60 * 1000);
    const estimatedCompletion = new Date(
      estimatedArrival.getTime() + request.estimatedDuration * 60 * 60 * 1000
    );

    // Generate reasoning
    const reasoning = this.generateReasoning(technician, request, {
      skillMatch,
      locationProximity,
      availability,
      workload,
      performance,
      customerPreference,
    });

    return {
      technicianId: technician.id,
      confidence: Math.min(totalScore / 100, 1),
      score: Math.round(totalScore),
      reasoning,
      factors: {
        skillMatch,
        locationProximity,
        availability,
        workload,
        performance,
        customerPreference,
      },
      estimatedArrival,
      estimatedCompletion,
      travelTime,
    };
  }

  /**
   * Calculate skill match score
   */
  private calculateSkillMatch(technicianSkills: string[], requiredSkills: string[]): number {
    if (requiredSkills.length === 0) return 100;

    const matchedSkills = requiredSkills.filter(skill =>
      technicianSkills.some(techSkill =>
        techSkill.toLowerCase().includes(skill.toLowerCase()) ||
        skill.toLowerCase().includes(techSkill.toLowerCase())
      )
    );

    return (matchedSkills.length / requiredSkills.length) * 100;
  }

  /**
   * Calculate location proximity score
   */
  private async calculateLocationProximity(
    technicianLocation?: { lat: number; lng: number },
    requestLocation?: { lat: number; lng: number }
  ): Promise<number> {
    if (!technicianLocation || !requestLocation) return 50; // Default score

    const distance = this.calculateDistance(
      technicianLocation.lat,
      technicianLocation.lng,
      requestLocation.lat,
      requestLocation.lng
    );

    // Score decreases with distance (max 50km for full score)
    return Math.max(0, 100 - (distance / 50) * 100);
  }

  /**
   * Calculate availability score
   */
  private calculateAvailability(technician: TechnicianProfile, preferredDate?: Date): number {
    if (technician.availability.status === 'OFFLINE') return 0;
    if (technician.availability.status === 'BUSY') return 30;
    if (technician.availability.status === 'ON_BREAK') return 70;

    // Check time off
    if (preferredDate && technician.availability.timeOff) {
      const isOnTimeOff = technician.availability.timeOff.some(timeOff =>
        preferredDate >= timeOff.start && preferredDate <= timeOff.end
      );
      if (isOnTimeOff) return 0;
    }

    return 100;
  }

  /**
   * Calculate workload score
   */
  private calculateWorkloadScore(workload: TechnicianProfile['workload']): number {
    const utilizationRate = workload.currentTasks / workload.maxCapacity;
    return Math.max(0, 100 - (utilizationRate * 100));
  }

  /**
   * Calculate performance score
   */
  private calculatePerformanceScore(performance: TechnicianProfile['performance']): number {
    const avgScore = (
      (performance.customerSatisfaction / 5) * 25 +
      (performance.qualityScore / 5) * 25 +
      (Math.min(performance.taskCompletionTime, 1.2) / 1.2) * 25 +
      (Math.max(0, 1 - performance.averageResponseTime / 60) * 25)
    );

    return Math.round(avgScore);
  }

  /**
   * Calculate customer preference score
   */
  private calculateCustomerPreference(
    technician: TechnicianProfile,
    request: ServiceRequest
  ): number {
    // Check if customer has preference for this technician
    if (request.customerPreferences?.preferredTechnician === technician.id) {
      return 100;
    }

    // Check if technician prefers this customer
    if (technician.preferences.preferredCustomers?.includes(request.customerId)) {
      return 80;
    }

    // Check if technician wants to avoid this customer
    if (technician.preferences.avoidCustomers?.includes(request.customerId)) {
      return 0;
    }

    return 50; // Neutral
  }

  /**
   * Refine recommendations using AI
   */
  private async refineWithAI(
    recommendations: DispatchRecommendation[],
    request: ServiceRequest
  ): Promise<DispatchRecommendation[]> {
    try {
      const prompt = `
Analyze these technician recommendations for an HVAC service request and provide insights:

Service Request:
- Type: ${request.serviceType}
- Priority: ${request.priority}
- Equipment: ${request.equipmentType}
- Complexity: ${request.complexity}
- Required Skills: ${request.requiredSkills.join(', ')}
- Location: ${request.location.address}

Top 3 Technician Recommendations:
${recommendations.slice(0, 3).map((rec, index) => `
${index + 1}. Technician ID: ${rec.technicianId}
   Score: ${rec.score}/100
   Factors: Skill(${rec.factors.skillMatch}), Location(${rec.factors.locationProximity}),
           Availability(${rec.factors.availability}), Performance(${rec.factors.performance})
   Travel Time: ${rec.travelTime} minutes
   Reasoning: ${rec.reasoning}
`).join('')}

Provide analysis and any adjustments to confidence scores based on:
1. Service complexity vs technician capabilities
2. Urgency vs response time
3. Customer satisfaction factors
4. Risk assessment

Return JSON with adjusted confidence scores and additional insights.
      `;

      const response = await bielikService.generateResponse({
        prompt,
        model: 'bielik-v3',
        temperature: 0.3,
        maxTokens: 1000,
      });

      // Parse AI response and adjust recommendations
      try {
        const aiInsights = JSON.parse(response.content);

        // Apply AI adjustments to confidence scores
        recommendations.forEach((rec, index) => {
          if (aiInsights.adjustments && aiInsights.adjustments[index]) {
            rec.confidence = Math.min(1, rec.confidence * aiInsights.adjustments[index].factor);
            rec.reasoning += ` AI Insight: ${aiInsights.adjustments[index].reason}`;
          }
        });
      } catch (parseError) {
        console.log('AI response parsing failed, using original recommendations');
      }

      return recommendations;
    } catch (error) {
      console.error('Error refining with AI:', error);
      return recommendations; // Return original if AI fails
    }
  }

  /**
   * Generate reasoning for recommendation
   */
  private generateReasoning(
    technician: TechnicianProfile,
    request: ServiceRequest,
    factors: any
  ): string {
    const reasons = [];

    if (factors.skillMatch > 80) {
      reasons.push(`Excellent skill match (${Math.round(factors.skillMatch)}%)`);
    } else if (factors.skillMatch > 60) {
      reasons.push(`Good skill match (${Math.round(factors.skillMatch)}%)`);
    } else {
      reasons.push(`Limited skill match (${Math.round(factors.skillMatch)}%)`);
    }

    if (factors.locationProximity > 80) {
      reasons.push('Very close to customer location');
    } else if (factors.locationProximity > 60) {
      reasons.push('Reasonable travel distance');
    } else {
      reasons.push('Significant travel required');
    }

    if (factors.performance > 80) {
      reasons.push('High performance rating');
    }

    if (factors.availability === 100) {
      reasons.push('Immediately available');
    } else if (factors.availability > 70) {
      reasons.push('Available with minor scheduling');
    }

    if (factors.workload > 80) {
      reasons.push('Low current workload');
    } else if (factors.workload < 40) {
      reasons.push('High current workload');
    }

    return reasons.join(', ');
  }

  /**
   * Calculate distance between two coordinates
   */
  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Calculate travel time (simplified)
   */
  private async calculateTravelTime(
    from?: { lat: number; lng: number },
    to?: { lat: number; lng: number }
  ): Promise<number> {
    if (!from || !to) return 30; // Default 30 minutes

    const distance = this.calculateDistance(from.lat, from.lng, to.lat, to.lng);

    // Assume average speed of 40 km/h in city traffic
    return Math.round((distance / 40) * 60);
  }

  /**
   * Convert database user to technician profile
   */
  private convertToTechnicianProfile(user: any): TechnicianProfile {
    const metadata = user.metadata || {};

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      skills: metadata.skills || [],
      certifications: metadata.certifications || [],
      experience: metadata.experience || 1,
      rating: metadata.rating || 4.0,
      currentLocation: metadata.currentLocation,
      availability: {
        status: metadata.availability?.status || 'AVAILABLE',
        schedule: metadata.availability?.schedule || {},
        timeOff: metadata.availability?.timeOff || [],
      },
      workload: {
        currentTasks: metadata.workload?.currentTasks || 0,
        maxCapacity: metadata.workload?.maxCapacity || 8,
        averageTaskDuration: metadata.workload?.averageTaskDuration || 4,
        completionRate: metadata.workload?.completionRate || 0.95,
      },
      preferences: {
        serviceTypes: metadata.preferences?.serviceTypes || [],
        maxTravelDistance: metadata.preferences?.maxTravelDistance || 50,
        preferredCustomers: metadata.preferences?.preferredCustomers || [],
        avoidCustomers: metadata.preferences?.avoidCustomers || [],
      },
      performance: {
        averageResponseTime: metadata.performance?.averageResponseTime || 15,
        customerSatisfaction: metadata.performance?.customerSatisfaction || 4.2,
        taskCompletionTime: metadata.performance?.taskCompletionTime || 1.0,
        qualityScore: metadata.performance?.qualityScore || 4.0,
      },
    };
  }

  /**
   * Convert service order to service request
   */
  private async convertToServiceRequest(serviceOrder: any): Promise<ServiceRequest> {
    const metadata = serviceOrder.metadata || {};

    // Get customer location (simplified - would use geocoding in production)
    const customerLocation = {
      lat: metadata.location?.lat || 52.2297, // Default Warsaw coordinates
      lng: metadata.location?.lng || 21.0122,
      address: serviceOrder.customer.address || 'Unknown location',
    };

    return {
      id: serviceOrder.id,
      customerId: serviceOrder.customerId,
      serviceType: serviceOrder.serviceType,
      priority: serviceOrder.priority,
      equipmentType: metadata.equipmentType || 'HVAC',
      location: customerLocation,
      estimatedDuration: serviceOrder.estimatedDuration || 4,
      requiredSkills: metadata.requiredSkills || [],
      preferredDate: serviceOrder.scheduledDate,
      customerPreferences: metadata.customerPreferences,
      complexity: metadata.complexity || 'MODERATE',
      urgency: {
        level: serviceOrder.priority === 'URGENT' ? 9 :
               serviceOrder.priority === 'HIGH' ? 7 :
               serviceOrder.priority === 'MEDIUM' ? 5 : 3,
        reason: metadata.urgencyReason,
        deadline: serviceOrder.dueDate,
      },
    };
  }

  /**
   * Assign technician to service order
   */
  private async assignTechnician(
    serviceOrderId: string,
    technicianId: string,
    recommendation: DispatchRecommendation
  ): Promise<TechnicianProfile> {
    // Update service order
    await prisma.serviceOrder.update({
      where: { id: serviceOrderId },
      data: {
        assignedTechnicianId: technicianId,
        scheduledDate: recommendation.estimatedArrival,
        status: 'SCHEDULED',
        metadata: {
          dispatchRecommendation: recommendation,
          autoAssigned: true,
          assignmentTimestamp: new Date(),
        },
        updatedAt: new Date(),
      },
    });

    // Get technician profile
    const technician = await prisma.user.findUnique({
      where: { id: technicianId },
    });

    if (!technician) {
      throw new Error('Technician not found');
    }

    return this.convertToTechnicianProfile(technician);
  }

  /**
   * Send dispatch notifications
   */
  private async sendDispatchNotifications(
    serviceOrder: any,
    technician: TechnicianProfile,
    recommendation: DispatchRecommendation
  ): Promise<string[]> {
    const notifications: string[] = [];

    try {
      // Notify technician
      await prisma.notification.create({
        data: {
          userId: technician.id,
          type: 'DISPATCH_ASSIGNMENT',
          title: 'New Service Assignment',
          message: `You have been assigned to service order: ${serviceOrder.title}`,
          data: {
            serviceOrderId: serviceOrder.id,
            estimatedArrival: recommendation.estimatedArrival,
            customerLocation: serviceOrder.customer.address,
            travelTime: recommendation.travelTime,
          },
          read: false,
        },
      });
      notifications.push('Technician notified');

      // Notify customer
      if (serviceOrder.customer.email) {
        await sendCustomerCommunication({
          customerId: serviceOrder.customerId,
          userId: serviceOrder.userId,
          channel: 'EMAIL',
          subject: 'Service Technician Assigned',
          content: `Your service request has been assigned to ${technician.name}. Estimated arrival: ${recommendation.estimatedArrival.toLocaleString()}`,
          direction: 'OUTBOUND',
        });
        notifications.push('Customer notified via email');
      }

      // Notify dispatcher/manager
      await prisma.notification.create({
        data: {
          userId: serviceOrder.userId,
          type: 'DISPATCH_COMPLETED',
          title: 'Auto-Dispatch Completed',
          message: `Service order ${serviceOrder.id} assigned to ${technician.name} (Score: ${recommendation.score})`,
          data: {
            serviceOrderId: serviceOrder.id,
            technicianId: technician.id,
            recommendation,
          },
          read: false,
        },
      });
      notifications.push('Manager notified');

    } catch (error) {
      console.error('Error sending dispatch notifications:', error);
      notifications.push('Some notifications failed');
    }

    return notifications;
  }
}

// Export singleton instance
export const aiDispatchService = new AIDispatchService();

// Export convenience functions
export const findOptimalTechnicianAssignment = (request: ServiceRequest) =>
  aiDispatchService.findOptimalAssignment(request);

export const autoDispatchServiceOrder = (serviceOrderId: string) =>
  aiDispatchService.autoDispatch(serviceOrderId);

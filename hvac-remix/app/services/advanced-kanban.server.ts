/**
 * Advanced Kanban Service - 8-Stage HVAC Workflow Management
 * Implements comprehensive workflow with AI-powered automation
 * Part of FAZA 2: Workflow & Kanban Enhancement
 */

import { prisma } from '~/db.server';
import { bielikService } from './bielik.server';
import { sendCustomerCommunication } from './communication.service';

export type KanbanStage =
  | 'BACKLOG'           // Zgłoszone, nieskategoryzowane zadania
  | 'READY'             // Wstępnie zweryfikowane, czekające na ustalenie terminu
  | 'SCHEDULED'         // Zaplanowane daty i przydzieleni technicy
  | 'IN_PROGRESS'       // Aktualnie realizowane (montaż/serwis)
  | 'WAITING_PARTS'     // Zatrzymane z powodu brakujących części
  | 'QUALITY_CHECK'     // Kontrola jakości i akceptacja klienta
  | 'DONE'              // Ukończone, oczekujące na fakturę
  | 'INVOICED';         // Rozliczone, zamknięte

export interface KanbanCard {
  id: string;
  title: string;
  description: string;
  stage: KanbanStage;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  serviceType: 'INSTALLATION' | 'REPAIR' | 'MAINTENANCE' | 'INSPECTION';
  customerId: string;
  assignedTechnicianId?: string;
  estimatedDuration: number; // in hours
  scheduledDate?: Date;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: {
    equipmentType?: string;
    location?: string;
    requiredParts?: string[];
    specialInstructions?: string;
    customerNotes?: string;
    technicianNotes?: string;
    photos?: string[];
    documents?: string[];
  };
  customer: {
    id: string;
    name: string;
    phone?: string;
    email?: string;
    address?: string;
  };
  technician?: {
    id: string;
    name: string;
    skills: string[];
    currentLocation?: { lat: number; lng: number };
    availability: 'AVAILABLE' | 'BUSY' | 'OFFLINE';
  };
}

export interface KanbanBoard {
  stages: Record<KanbanStage, {
    name: string;
    description: string;
    color: string;
    cards: KanbanCard[];
    limits?: {
      min?: number;
      max?: number;
    };
    automation?: {
      autoMove?: boolean;
      conditions?: string[];
      notifications?: string[];
    };
  }>;
  metrics: {
    totalCards: number;
    averageCycleTime: number;
    bottlenecks: string[];
    efficiency: number;
  };
}

export interface StageTransition {
  from: KanbanStage;
  to: KanbanStage;
  cardId: string;
  userId: string;
  timestamp: Date;
  reason?: string;
  metadata?: Record<string, any>;
}

export interface AutomationRule {
  id: string;
  name: string;
  trigger: {
    stage?: KanbanStage;
    condition: string;
    timeDelay?: number;
  };
  action: {
    type: 'MOVE_CARD' | 'ASSIGN_TECHNICIAN' | 'SEND_NOTIFICATION' | 'CREATE_TASK' | 'ORDER_PARTS';
    parameters: Record<string, any>;
  };
  active: boolean;
}

/**
 * Advanced Kanban Service
 */
class AdvancedKanbanService {
  private stageDefinitions: Record<KanbanStage, any> = {
    BACKLOG: {
      name: 'Backlog',
      description: 'Zgłoszone, nieskategoryzowane zadania',
      color: '#6B7280',
      automation: {
        autoMove: true,
        conditions: ['priority_assessment_complete'],
        notifications: ['new_task_alert'],
      },
    },
    READY: {
      name: 'Ready for Scheduling',
      description: 'Wstępnie zweryfikowane, czekające na ustalenie terminu',
      color: '#3B82F6',
      automation: {
        autoMove: false,
        notifications: ['scheduling_required'],
      },
    },
    SCHEDULED: {
      name: 'Scheduled',
      description: 'Zaplanowane daty i przydzieleni technicy',
      color: '#8B5CF6',
      automation: {
        autoMove: true,
        conditions: ['technician_assigned', 'date_confirmed'],
        notifications: ['customer_confirmation', 'technician_notification'],
      },
    },
    IN_PROGRESS: {
      name: 'In Progress',
      description: 'Aktualnie realizowane (montaż/serwis)',
      color: '#F59E0B',
      limits: { max: 10 }, // Limit concurrent work
      automation: {
        autoMove: false,
        notifications: ['progress_updates'],
      },
    },
    WAITING_PARTS: {
      name: 'Waiting for Parts',
      description: 'Zatrzymane z powodu brakujących części',
      color: '#EF4444',
      automation: {
        autoMove: true,
        conditions: ['parts_ordered'],
        notifications: ['parts_delay_alert', 'customer_update'],
      },
    },
    QUALITY_CHECK: {
      name: 'Quality Check',
      description: 'Kontrola jakości i akceptacja klienta',
      color: '#10B981',
      automation: {
        autoMove: true,
        conditions: ['quality_approved', 'customer_satisfied'],
        notifications: ['quality_review_required'],
      },
    },
    DONE: {
      name: 'Done',
      description: 'Ukończone, oczekujące na fakturę',
      color: '#059669',
      automation: {
        autoMove: true,
        conditions: ['invoice_generated'],
        notifications: ['billing_required'],
      },
    },
    INVOICED: {
      name: 'Invoiced',
      description: 'Rozliczone, zamknięte',
      color: '#374151',
      automation: {
        autoMove: false,
        notifications: ['task_completed'],
      },
    },
  };

  private automationRules: Map<string, AutomationRule> = new Map();

  constructor() {
    this.initializeAutomationRules();
  }

  /**
   * Get complete kanban board with all stages and cards
   */
  async getKanbanBoard(filters?: {
    technicianId?: string;
    customerId?: string;
    priority?: string;
    serviceType?: string;
    dateRange?: { start: Date; end: Date };
  }): Promise<KanbanBoard> {
    try {
      const whereClause: any = {};

      if (filters?.technicianId) {
        whereClause.assignedTechnicianId = filters.technicianId;
      }

      if (filters?.customerId) {
        whereClause.customerId = filters.customerId;
      }

      if (filters?.priority) {
        whereClause.priority = filters.priority;
      }

      if (filters?.serviceType) {
        whereClause.serviceType = filters.serviceType;
      }

      if (filters?.dateRange) {
        whereClause.createdAt = {
          gte: filters.dateRange.start,
          lte: filters.dateRange.end,
        };
      }

      const serviceOrders = await prisma.serviceOrder.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
              address: true,
            },
          },
          assignedTechnician: {
            select: {
              id: true,
              name: true,
              skills: true,
              metadata: true,
            },
          },
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'asc' },
        ],
      });

      // Group cards by stage
      const stages: Record<KanbanStage, any> = {} as any;

      Object.entries(this.stageDefinitions).forEach(([stage, definition]) => {
        stages[stage as KanbanStage] = {
          ...definition,
          cards: [],
        };
      });

      serviceOrders.forEach(order => {
        const card: KanbanCard = {
          id: order.id,
          title: order.title,
          description: order.description || '',
          stage: (order.status as KanbanStage) || 'BACKLOG',
          priority: order.priority as any,
          serviceType: order.serviceType as any,
          customerId: order.customerId,
          assignedTechnicianId: order.assignedTechnicianId || undefined,
          estimatedDuration: order.estimatedDuration || 4,
          scheduledDate: order.scheduledDate || undefined,
          dueDate: order.dueDate || undefined,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
          metadata: {
            equipmentType: (order.metadata as any)?.equipmentType,
            location: (order.metadata as any)?.location,
            requiredParts: (order.metadata as any)?.requiredParts || [],
            specialInstructions: (order.metadata as any)?.specialInstructions,
            customerNotes: (order.metadata as any)?.customerNotes,
            technicianNotes: (order.metadata as any)?.technicianNotes,
            photos: (order.metadata as any)?.photos || [],
            documents: (order.metadata as any)?.documents || [],
          },
          customer: order.customer,
          technician: order.assignedTechnician ? {
            id: order.assignedTechnician.id,
            name: order.assignedTechnician.name,
            skills: order.assignedTechnician.skills || [],
            currentLocation: (order.assignedTechnician.metadata as any)?.currentLocation,
            availability: (order.assignedTechnician.metadata as any)?.availability || 'AVAILABLE',
          } : undefined,
        };

        stages[card.stage].cards.push(card);
      });

      // Calculate metrics
      const metrics = await this.calculateBoardMetrics(serviceOrders);

      return {
        stages,
        metrics,
      };
    } catch (error) {
      console.error('Error getting kanban board:', error);
      throw new Error('Failed to get kanban board');
    }
  }

  /**
   * Move card between stages with validation and automation
   */
  async moveCard(
    cardId: string,
    fromStage: KanbanStage,
    toStage: KanbanStage,
    userId: string,
    metadata?: Record<string, any>
  ): Promise<{ success: boolean; message?: string }> {
    try {
      // Validate transition
      const isValidTransition = this.validateStageTransition(fromStage, toStage);
      if (!isValidTransition.valid) {
        return { success: false, message: isValidTransition.reason };
      }

      // Check stage limits
      const stageLimitCheck = await this.checkStageLimits(toStage, cardId);
      if (!stageLimitCheck.allowed) {
        return { success: false, message: stageLimitCheck.reason };
      }

      // Update service order status
      await prisma.serviceOrder.update({
        where: { id: cardId },
        data: {
          status: toStage,
          updatedAt: new Date(),
          metadata: metadata ? {
            ...(await this.getCardMetadata(cardId)),
            ...metadata,
          } : undefined,
        },
      });

      // Record transition
      await this.recordStageTransition({
        from: fromStage,
        to: toStage,
        cardId,
        userId,
        timestamp: new Date(),
        metadata,
      });

      // Execute automation rules
      await this.executeAutomationRules(cardId, toStage, metadata);

      // Send notifications
      await this.sendStageTransitionNotifications(cardId, fromStage, toStage);

      return { success: true };
    } catch (error) {
      console.error('Error moving card:', error);
      return { success: false, message: 'Failed to move card' };
    }
  }

  /**
   * Validate stage transition rules
   */
  private validateStageTransition(from: KanbanStage, to: KanbanStage): { valid: boolean; reason?: string } {
    // Define allowed transitions
    const allowedTransitions: Record<KanbanStage, KanbanStage[]> = {
      BACKLOG: ['READY', 'SCHEDULED'], // Can skip to scheduled for urgent tasks
      READY: ['SCHEDULED', 'BACKLOG'],
      SCHEDULED: ['IN_PROGRESS', 'READY'],
      IN_PROGRESS: ['WAITING_PARTS', 'QUALITY_CHECK', 'SCHEDULED'], // Can go back if rescheduled
      WAITING_PARTS: ['IN_PROGRESS', 'SCHEDULED'], // Resume work or reschedule
      QUALITY_CHECK: ['DONE', 'IN_PROGRESS'], // Approve or send back for fixes
      DONE: ['INVOICED', 'QUALITY_CHECK'], // Invoice or reopen for issues
      INVOICED: [], // Final stage - no transitions allowed
    };

    const allowed = allowedTransitions[from] || [];

    if (!allowed.includes(to)) {
      return {
        valid: false,
        reason: `Transition from ${from} to ${to} is not allowed`,
      };
    }

    return { valid: true };
  }

  /**
   * Check stage limits (WIP limits)
   */
  private async checkStageLimits(stage: KanbanStage, excludeCardId?: string): Promise<{ allowed: boolean; reason?: string }> {
    const stageDefinition = this.stageDefinitions[stage];

    if (!stageDefinition.limits?.max) {
      return { allowed: true };
    }

    const currentCount = await prisma.serviceOrder.count({
      where: {
        status: stage,
        id: excludeCardId ? { not: excludeCardId } : undefined,
      },
    });

    if (currentCount >= stageDefinition.limits.max) {
      return {
        allowed: false,
        reason: `Stage ${stage} has reached its limit of ${stageDefinition.limits.max} items`,
      };
    }

    return { allowed: true };
  }

  /**
   * Get card metadata
   */
  private async getCardMetadata(cardId: string): Promise<Record<string, any>> {
    const serviceOrder = await prisma.serviceOrder.findUnique({
      where: { id: cardId },
      select: { metadata: true },
    });

    return (serviceOrder?.metadata as Record<string, any>) || {};
  }

  /**
   * Record stage transition for analytics
   */
  private async recordStageTransition(transition: StageTransition): Promise<void> {
    try {
      await prisma.workflowTransition.create({
        data: {
          serviceOrderId: transition.cardId,
          fromStage: transition.from,
          toStage: transition.to,
          userId: transition.userId,
          timestamp: transition.timestamp,
          reason: transition.reason,
          metadata: transition.metadata,
        },
      });
    } catch (error) {
      console.error('Error recording stage transition:', error);
    }
  }

  /**
   * Execute automation rules for stage transitions
   */
  private async executeAutomationRules(cardId: string, newStage: KanbanStage, metadata?: Record<string, any>): Promise<void> {
    try {
      for (const [ruleId, rule] of this.automationRules) {
        if (!rule.active) continue;

        if (rule.trigger.stage && rule.trigger.stage !== newStage) continue;

        // Check if conditions are met
        const conditionsMet = await this.evaluateRuleConditions(rule, cardId, metadata);

        if (conditionsMet) {
          await this.executeRuleAction(rule, cardId);
        }
      }
    } catch (error) {
      console.error('Error executing automation rules:', error);
    }
  }

  /**
   * Evaluate rule conditions
   */
  private async evaluateRuleConditions(rule: AutomationRule, cardId: string, metadata?: Record<string, any>): Promise<boolean> {
    // Simplified condition evaluation - in production, this would be more sophisticated
    const condition = rule.trigger.condition;

    switch (condition) {
      case 'priority_assessment_complete':
        return metadata?.priorityAssessed === true;

      case 'technician_assigned':
        const order = await prisma.serviceOrder.findUnique({
          where: { id: cardId },
          select: { assignedTechnicianId: true },
        });
        return !!order?.assignedTechnicianId;

      case 'date_confirmed':
        const orderWithDate = await prisma.serviceOrder.findUnique({
          where: { id: cardId },
          select: { scheduledDate: true },
        });
        return !!orderWithDate?.scheduledDate;

      case 'parts_ordered':
        return metadata?.partsOrdered === true;

      case 'quality_approved':
        return metadata?.qualityApproved === true;

      case 'customer_satisfied':
        return metadata?.customerSatisfied === true;

      case 'invoice_generated':
        return metadata?.invoiceGenerated === true;

      default:
        return false;
    }
  }

  /**
   * Execute rule action
   */
  private async executeRuleAction(rule: AutomationRule, cardId: string): Promise<void> {
    try {
      switch (rule.action.type) {
        case 'MOVE_CARD':
          const targetStage = rule.action.parameters.targetStage as KanbanStage;
          const currentOrder = await prisma.serviceOrder.findUnique({
            where: { id: cardId },
            select: { status: true },
          });

          if (currentOrder) {
            await this.moveCard(
              cardId,
              currentOrder.status as KanbanStage,
              targetStage,
              'SYSTEM', // System user for automated actions
              { automatedMove: true, ruleId: rule.id }
            );
          }
          break;

        case 'ASSIGN_TECHNICIAN':
          await this.autoAssignTechnician(cardId, rule.action.parameters);
          break;

        case 'SEND_NOTIFICATION':
          await this.sendAutomationNotification(cardId, rule.action.parameters);
          break;

        case 'CREATE_TASK':
          await this.createAutomatedTask(cardId, rule.action.parameters);
          break;

        case 'ORDER_PARTS':
          await this.autoOrderParts(cardId, rule.action.parameters);
          break;
      }
    } catch (error) {
      console.error(`Error executing rule action ${rule.action.type}:`, error);
    }
  }

  /**
   * Send stage transition notifications
   */
  private async sendStageTransitionNotifications(cardId: string, fromStage: KanbanStage, toStage: KanbanStage): Promise<void> {
    try {
      const serviceOrder = await prisma.serviceOrder.findUnique({
        where: { id: cardId },
        include: {
          customer: true,
          assignedTechnician: true,
        },
      });

      if (!serviceOrder) return;

      const stageDefinition = this.stageDefinitions[toStage];
      const notifications = stageDefinition.automation?.notifications || [];

      for (const notificationType of notifications) {
        await this.sendNotificationByType(notificationType, serviceOrder, fromStage, toStage);
      }
    } catch (error) {
      console.error('Error sending stage transition notifications:', error);
    }
  }

  /**
   * Send notification by type
   */
  private async sendNotificationByType(
    type: string,
    serviceOrder: any,
    fromStage: KanbanStage,
    toStage: KanbanStage
  ): Promise<void> {
    try {
      switch (type) {
        case 'customer_confirmation':
          await this.sendCustomerConfirmation(serviceOrder, toStage);
          break;

        case 'technician_notification':
          await this.sendTechnicianNotification(serviceOrder, toStage);
          break;

        case 'progress_updates':
          await this.sendProgressUpdate(serviceOrder, toStage);
          break;

        case 'parts_delay_alert':
          await this.sendPartsDelayAlert(serviceOrder);
          break;

        case 'quality_review_required':
          await this.sendQualityReviewAlert(serviceOrder);
          break;

        case 'billing_required':
          await this.sendBillingAlert(serviceOrder);
          break;
      }
    } catch (error) {
      console.error(`Error sending notification type ${type}:`, error);
    }
  }

  /**
   * Calculate board metrics
   */
  private async calculateBoardMetrics(serviceOrders: any[]): Promise<any> {
    const totalCards = serviceOrders.length;

    // Calculate average cycle time (simplified)
    const completedOrders = serviceOrders.filter(order =>
      ['DONE', 'INVOICED'].includes(order.status)
    );

    let totalCycleTime = 0;
    for (const order of completedOrders) {
      const cycleTime = order.updatedAt.getTime() - order.createdAt.getTime();
      totalCycleTime += cycleTime;
    }

    const averageCycleTime = completedOrders.length > 0
      ? totalCycleTime / completedOrders.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0;

    // Identify bottlenecks (stages with most cards)
    const stageCounts: Record<string, number> = {};
    serviceOrders.forEach(order => {
      stageCounts[order.status] = (stageCounts[order.status] || 0) + 1;
    });

    const bottlenecks = Object.entries(stageCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 2)
      .map(([stage]) => stage);

    // Calculate efficiency (completed vs total)
    const efficiency = totalCards > 0
      ? (completedOrders.length / totalCards) * 100
      : 0;

    return {
      totalCards,
      averageCycleTime: Math.round(averageCycleTime * 10) / 10,
      bottlenecks,
      efficiency: Math.round(efficiency),
    };
  }

  /**
   * Initialize automation rules
   */
  private initializeAutomationRules(): void {
    // Auto-move from BACKLOG to READY when priority is assessed
    this.automationRules.set('auto-ready', {
      id: 'auto-ready',
      name: 'Auto-move to Ready',
      trigger: {
        stage: 'BACKLOG',
        condition: 'priority_assessment_complete',
      },
      action: {
        type: 'MOVE_CARD',
        parameters: { targetStage: 'READY' },
      },
      active: true,
    });

    // Auto-assign technician when moved to SCHEDULED
    this.automationRules.set('auto-assign', {
      id: 'auto-assign',
      name: 'Auto-assign Technician',
      trigger: {
        stage: 'SCHEDULED',
        condition: 'technician_assigned',
      },
      action: {
        type: 'SEND_NOTIFICATION',
        parameters: { type: 'technician_assignment' },
      },
      active: true,
    });

    // Auto-order parts when moved to WAITING_PARTS
    this.automationRules.set('auto-parts', {
      id: 'auto-parts',
      name: 'Auto-order Parts',
      trigger: {
        stage: 'WAITING_PARTS',
        condition: 'parts_ordered',
      },
      action: {
        type: 'ORDER_PARTS',
        parameters: { urgent: true },
      },
      active: true,
    });
  }

  /**
   * AI-powered technician assignment
   */
  private async autoAssignTechnician(cardId: string, parameters: Record<string, any>): Promise<void> {
    try {
      const serviceOrder = await prisma.serviceOrder.findUnique({
        where: { id: cardId },
        include: { customer: true },
      });

      if (!serviceOrder) return;

      // Get available technicians
      const technicians = await prisma.user.findMany({
        where: {
          role: 'TECHNICIAN',
          metadata: {
            path: ['availability'],
            equals: 'AVAILABLE',
          },
        },
      });

      if (technicians.length === 0) return;

      // Use AI to find best match
      const prompt = `
Assign the best technician for this HVAC service order:

Service Type: ${serviceOrder.serviceType}
Priority: ${serviceOrder.priority}
Location: ${serviceOrder.customer.address}
Equipment: ${(serviceOrder.metadata as any)?.equipmentType || 'Unknown'}

Available Technicians:
${technicians.map(t => `
- ${t.name}: Skills: ${(t.metadata as any)?.skills?.join(', ') || 'General'}, Location: ${(t.metadata as any)?.currentLocation || 'Unknown'}
`).join('')}

Return the best technician ID and reason.
      `;

      const response = await bielikService.generateResponse({
        prompt,
        model: 'bielik-v3',
        temperature: 0.3,
      });

      // Parse AI response and assign technician (simplified)
      const bestTechnician = technicians[0]; // Fallback to first available

      await prisma.serviceOrder.update({
        where: { id: cardId },
        data: {
          assignedTechnicianId: bestTechnician.id,
          metadata: {
            ...(serviceOrder.metadata as any),
            autoAssigned: true,
            assignmentReason: response.content.substring(0, 200),
          },
        },
      });
    } catch (error) {
      console.error('Error auto-assigning technician:', error);
    }
  }

  /**
   * Send automation notification
   */
  private async sendAutomationNotification(cardId: string, parameters: Record<string, any>): Promise<void> {
    try {
      const serviceOrder = await prisma.serviceOrder.findUnique({
        where: { id: cardId },
        include: { customer: true, assignedTechnician: true },
      });

      if (!serviceOrder) return;

      const notificationType = parameters.type;
      let message = '';

      switch (notificationType) {
        case 'technician_assignment':
          message = `You have been assigned to service order: ${serviceOrder.title}`;
          break;
        default:
          message = `Service order ${serviceOrder.title} requires attention`;
      }

      await prisma.notification.create({
        data: {
          userId: serviceOrder.assignedTechnicianId || serviceOrder.userId,
          type: 'KANBAN_AUTOMATION',
          title: 'Automated Notification',
          message,
          data: { serviceOrderId: cardId, notificationType },
          read: false,
        },
      });
    } catch (error) {
      console.error('Error sending automation notification:', error);
    }
  }

  /**
   * Create automated task
   */
  private async createAutomatedTask(cardId: string, parameters: Record<string, any>): Promise<void> {
    try {
      const serviceOrder = await prisma.serviceOrder.findUnique({
        where: { id: cardId },
      });

      if (!serviceOrder) return;

      await prisma.task.create({
        data: {
          title: parameters.title || `Automated task for ${serviceOrder.title}`,
          description: parameters.description || 'Automatically created task',
          priority: serviceOrder.priority,
          status: 'PENDING',
          dueDate: parameters.dueDate ? new Date(parameters.dueDate) : new Date(Date.now() + 24 * 60 * 60 * 1000),
          assignedUserId: serviceOrder.assignedTechnicianId || serviceOrder.userId,
          serviceOrderId: cardId,
          metadata: { automated: true, ...parameters },
        },
      });
    } catch (error) {
      console.error('Error creating automated task:', error);
    }
  }

  /**
   * Auto-order parts
   */
  private async autoOrderParts(cardId: string, parameters: Record<string, any>): Promise<void> {
    try {
      const serviceOrder = await prisma.serviceOrder.findUnique({
        where: { id: cardId },
      });

      if (!serviceOrder) return;

      const requiredParts = (serviceOrder.metadata as any)?.requiredParts || [];

      if (requiredParts.length === 0) return;

      // Create parts order (simplified - would integrate with inventory system)
      await prisma.partsOrder.create({
        data: {
          serviceOrderId: cardId,
          parts: requiredParts,
          urgent: parameters.urgent || false,
          status: 'PENDING',
          metadata: { automated: true },
        },
      });

      // Update service order metadata
      await prisma.serviceOrder.update({
        where: { id: cardId },
        data: {
          metadata: {
            ...(serviceOrder.metadata as any),
            partsOrdered: true,
            partsOrderDate: new Date(),
          },
        },
      });
    } catch (error) {
      console.error('Error auto-ordering parts:', error);
    }
  }

  /**
   * Notification methods
   */
  private async sendCustomerConfirmation(serviceOrder: any, stage: KanbanStage): Promise<void> {
    const message = `Your service appointment has been ${stage === 'SCHEDULED' ? 'scheduled' : 'updated'}. We will contact you with details.`;

    await sendCustomerCommunication({
      customerId: serviceOrder.customerId,
      userId: serviceOrder.userId,
      channel: 'EMAIL',
      subject: 'Service Appointment Update',
      content: message,
      direction: 'OUTBOUND',
    });
  }

  private async sendTechnicianNotification(serviceOrder: any, stage: KanbanStage): Promise<void> {
    if (!serviceOrder.assignedTechnicianId) return;

    await prisma.notification.create({
      data: {
        userId: serviceOrder.assignedTechnicianId,
        type: 'KANBAN_UPDATE',
        title: 'Service Order Update',
        message: `Service order ${serviceOrder.title} moved to ${stage}`,
        data: { serviceOrderId: serviceOrder.id, stage },
        read: false,
      },
    });
  }

  private async sendProgressUpdate(serviceOrder: any, stage: KanbanStage): Promise<void> {
    const message = `Your service request is now ${stage.toLowerCase().replace('_', ' ')}. We'll keep you updated on progress.`;

    await sendCustomerCommunication({
      customerId: serviceOrder.customerId,
      userId: serviceOrder.userId,
      channel: 'SMS',
      content: message,
      direction: 'OUTBOUND',
    });
  }

  private async sendPartsDelayAlert(serviceOrder: any): Promise<void> {
    const message = `We're waiting for parts for your service order. We'll notify you once they arrive and we can continue.`;

    await sendCustomerCommunication({
      customerId: serviceOrder.customerId,
      userId: serviceOrder.userId,
      channel: 'EMAIL',
      subject: 'Service Update - Parts Delay',
      content: message,
      direction: 'OUTBOUND',
    });
  }

  private async sendQualityReviewAlert(serviceOrder: any): Promise<void> {
    await prisma.notification.create({
      data: {
        userId: serviceOrder.userId,
        type: 'QUALITY_REVIEW',
        title: 'Quality Review Required',
        message: `Service order ${serviceOrder.title} requires quality review`,
        data: { serviceOrderId: serviceOrder.id },
        read: false,
      },
    });
  }

  private async sendBillingAlert(serviceOrder: any): Promise<void> {
    await prisma.notification.create({
      data: {
        userId: serviceOrder.userId,
        type: 'BILLING_REQUIRED',
        title: 'Billing Required',
        message: `Service order ${serviceOrder.title} is ready for billing`,
        data: { serviceOrderId: serviceOrder.id },
        read: false,
      },
    });
  }
}

// Export singleton instance
export const advancedKanbanService = new AdvancedKanbanService();

// Export convenience functions
export const getKanbanBoard = (filters?: any) =>
  advancedKanbanService.getKanbanBoard(filters);

export const moveKanbanCard = (cardId: string, fromStage: KanbanStage, toStage: KanbanStage, userId: string, metadata?: any) =>
  advancedKanbanService.moveCard(cardId, fromStage, toStage, userId, metadata);

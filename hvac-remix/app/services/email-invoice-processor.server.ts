/**
 * FAZA 3A: Email Invoice Processor Service
 * Integrates with existing dual-source email processing system (Dolores + Grzegorz)
 * Processes invoice attachments using OCR pipeline and stores in enhanced financial schema
 */

import { prisma } from "~/db.server";
import { gobackendClient } from "~/lib/gobackend-client";

// =====================================================
// TYPES & INTERFACES
// =====================================================

export interface InvoiceExtractionResult {
  success: boolean;
  invoiceNumber?: string;
  amount?: number;
  dueDate?: Date;
  supplierName?: string;
  supplierEmail?: string;
  lineItems?: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }>;
  confidence: number;
  extractedText: string;
  errors?: string[];
}

export interface ProcessedInvoiceData {
  id: string;
  emailId: string;
  attachmentId: string;
  extractionResult: InvoiceExtractionResult;
  status: 'PENDING' | 'PROCESSED' | 'FAILED' | 'REQUIRES_VALIDATION';
  processedAt: Date;
  validatedAt?: Date;
  validatedBy?: string;
  journalEntryId?: string;
}

// =====================================================
// EMAIL INVOICE PROCESSOR SERVICE
// =====================================================

export class EmailInvoiceProcessorService {

  /**
   * Process invoice attachments from dual-source email system
   */
  static async processInvoiceAttachments(userId: string): Promise<ProcessedInvoiceData[]> {
    try {
      // Get unprocessed email attachments that could be invoices
      const unprocessedAttachments = await prisma.emailAttachment.findMany({
        where: {
          email: { userId },
          isProcessed: false,
          OR: [
            { fileName: { contains: 'invoice', mode: 'insensitive' } },
            { fileName: { contains: 'bill', mode: 'insensitive' } },
            { fileName: { contains: 'receipt', mode: 'insensitive' } },
            { fileName: { endsWith: '.pdf', mode: 'insensitive' } },
            { fileName: { endsWith: '.docx', mode: 'insensitive' } },
            { fileName: { endsWith: '.xlsx', mode: 'insensitive' } }
          ]
        },
        include: {
          email: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 50 // Process in batches
      });

      const processedInvoices: ProcessedInvoiceData[] = [];

      for (const attachment of unprocessedAttachments) {
        try {
          // Extract invoice data using OCR and AI
          const extractionResult = await this.extractInvoiceData(attachment);
          
          // Determine processing status based on extraction confidence
          const status = this.determineProcessingStatus(extractionResult);
          
          // Create processed invoice record
          const processedInvoice = await prisma.processedInvoice.create({
            data: {
              emailId: attachment.email.id,
              attachmentId: attachment.id,
              extractionResult: JSON.stringify(extractionResult),
              status,
              processedAt: new Date(),
              confidence: extractionResult.confidence
            }
          });

          // If extraction was successful and confident, create journal entry
          if (status === 'PROCESSED' && extractionResult.success && extractionResult.amount) {
            await this.createJournalEntryFromInvoice(extractionResult, userId, processedInvoice.id);
          }

          // Mark attachment as processed
          await prisma.emailAttachment.update({
            where: { id: attachment.id },
            data: { isProcessed: true }
          });

          processedInvoices.push({
            id: processedInvoice.id,
            emailId: attachment.email.id,
            attachmentId: attachment.id,
            extractionResult,
            status,
            processedAt: new Date()
          });

        } catch (error) {
          console.error(`Error processing attachment ${attachment.id}:`, error);
          
          // Mark as failed
          await prisma.processedInvoice.create({
            data: {
              emailId: attachment.email.id,
              attachmentId: attachment.id,
              extractionResult: JSON.stringify({
                success: false,
                confidence: 0,
                extractedText: '',
                errors: [error instanceof Error ? error.message : 'Unknown error']
              }),
              status: 'FAILED',
              processedAt: new Date(),
              confidence: 0
            }
          });
        }
      }

      return processedInvoices;
    } catch (error) {
      console.error('Error processing invoice attachments:', error);
      throw new Error('Failed to process invoice attachments');
    }
  }

  /**
   * Extract invoice data from attachment using OCR and AI
   */
  private static async extractInvoiceData(attachment: any): Promise<InvoiceExtractionResult> {
    try {
      // Use existing OCR pipeline to extract text
      let extractedText = attachment.textContent || '';
      
      // If no text content, trigger OCR processing
      if (!extractedText && attachment.filePath) {
        extractedText = await this.performOCRExtraction(attachment.filePath);
      }

      // Use AI to parse invoice data from extracted text
      const parsedData = await this.parseInvoiceWithAI(extractedText);
      
      // Calculate confidence based on extracted data quality
      const confidence = this.calculateExtractionConfidence(parsedData, extractedText);

      return {
        success: confidence > 0.6,
        ...parsedData,
        confidence,
        extractedText
      };
    } catch (error) {
      console.error('Error extracting invoice data:', error);
      return {
        success: false,
        confidence: 0,
        extractedText: '',
        errors: [error instanceof Error ? error.message : 'Extraction failed']
      };
    }
  }

  /**
   * Parse invoice data using AI (Bielik V3/Gemma3)
   */
  private static async parseInvoiceWithAI(text: string): Promise<Partial<InvoiceExtractionResult>> {
    try {
      // Enhanced regex patterns for invoice data extraction
      const patterns = {
        invoiceNumber: [
          /invoice\s*#?\s*:?\s*([A-Z0-9\-]+)/i,
          /inv\s*#?\s*:?\s*([A-Z0-9\-]+)/i,
          /bill\s*#?\s*:?\s*([A-Z0-9\-]+)/i
        ],
        amount: [
          /total\s*:?\s*\$?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/i,
          /amount\s*due\s*:?\s*\$?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/i,
          /balance\s*:?\s*\$?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/i
        ],
        dueDate: [
          /due\s*date\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/i,
          /payment\s*due\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/i
        ],
        supplier: [
          /from\s*:?\s*([A-Za-z\s&\.,]+)(?:\n|\r)/i,
          /bill\s*from\s*:?\s*([A-Za-z\s&\.,]+)(?:\n|\r)/i
        ]
      };

      const result: Partial<InvoiceExtractionResult> = {};

      // Extract invoice number
      for (const pattern of patterns.invoiceNumber) {
        const match = text.match(pattern);
        if (match) {
          result.invoiceNumber = match[1].trim();
          break;
        }
      }

      // Extract amount
      for (const pattern of patterns.amount) {
        const match = text.match(pattern);
        if (match) {
          result.amount = parseFloat(match[1].replace(/,/g, ''));
          break;
        }
      }

      // Extract due date
      for (const pattern of patterns.dueDate) {
        const match = text.match(pattern);
        if (match) {
          const dateStr = match[1];
          const parsedDate = new Date(dateStr);
          if (!isNaN(parsedDate.getTime())) {
            result.dueDate = parsedDate;
          }
          break;
        }
      }

      // Extract supplier name
      for (const pattern of patterns.supplier) {
        const match = text.match(pattern);
        if (match) {
          result.supplierName = match[1].trim().replace(/[^\w\s&\.,]/g, '');
          break;
        }
      }

      // Extract email if present
      const emailMatch = text.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      if (emailMatch) {
        result.supplierEmail = emailMatch[1];
      }

      // Extract line items (simplified)
      const lineItems = this.extractLineItems(text);
      if (lineItems.length > 0) {
        result.lineItems = lineItems;
      }

      return result;
    } catch (error) {
      console.error('Error parsing invoice with AI:', error);
      return {};
    }
  }

  /**
   * Extract line items from invoice text
   */
  private static extractLineItems(text: string): Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }> {
    const lineItems = [];
    
    // Look for table-like structures with quantities and prices
    const lines = text.split('\n');
    
    for (const line of lines) {
      // Pattern: Description Qty Price Total
      const match = line.match(/(.+?)\s+(\d+)\s+\$?(\d+\.?\d*)\s+\$?(\d+\.?\d*)/);
      if (match) {
        const [, description, qty, unitPrice, totalPrice] = match;
        lineItems.push({
          description: description.trim(),
          quantity: parseInt(qty),
          unitPrice: parseFloat(unitPrice),
          totalPrice: parseFloat(totalPrice)
        });
      }
    }

    return lineItems;
  }

  /**
   * Calculate extraction confidence based on data quality
   */
  private static calculateExtractionConfidence(data: Partial<InvoiceExtractionResult>, text: string): number {
    let confidence = 0.2; // Base confidence

    // Check for key invoice indicators
    if (data.invoiceNumber) confidence += 0.25;
    if (data.amount && data.amount > 0) confidence += 0.3;
    if (data.dueDate) confidence += 0.15;
    if (data.supplierName) confidence += 0.1;

    // Check text quality
    if (text.length > 100) confidence += 0.1;
    if (/invoice|bill|payment/i.test(text)) confidence += 0.1;

    return Math.min(confidence, 1.0);
  }

  /**
   * Determine processing status based on extraction result
   */
  private static determineProcessingStatus(result: InvoiceExtractionResult): 'PENDING' | 'PROCESSED' | 'FAILED' | 'REQUIRES_VALIDATION' {
    if (!result.success) return 'FAILED';
    if (result.confidence >= 0.8) return 'PROCESSED';
    if (result.confidence >= 0.6) return 'REQUIRES_VALIDATION';
    return 'PENDING';
  }

  /**
   * Create journal entry from extracted invoice data
   */
  private static async createJournalEntryFromInvoice(
    invoiceData: InvoiceExtractionResult,
    userId: string,
    processedInvoiceId: string
  ): Promise<void> {
    try {
      if (!invoiceData.amount) return;

      // Generate journal entry number
      const entryNumber = await this.generateJournalEntryNumber();

      // Create journal entry for the invoice
      const journalEntry = await prisma.journalEntry.create({
        data: {
          entryNumber,
          entryDate: new Date(),
          description: `Invoice from ${invoiceData.supplierName || 'Unknown Supplier'} - ${invoiceData.invoiceNumber || 'No Number'}`,
          reference: `INV-${invoiceData.invoiceNumber || processedInvoiceId}`,
          totalDebit: invoiceData.amount,
          totalCredit: invoiceData.amount,
          status: 'POSTED',
          createdById: userId
        }
      });

      // Create journal entry lines (double-entry bookkeeping)
      await prisma.journalEntryLine.createMany({
        data: [
          {
            // Debit: Expense account
            debitAmount: invoiceData.amount,
            creditAmount: 0,
            description: `Expense - ${invoiceData.supplierName || 'Supplier'}`,
            journalEntryId: journalEntry.id,
            accountId: 'acc_6000' // Operating Expenses account
          },
          {
            // Credit: Accounts Payable
            debitAmount: 0,
            creditAmount: invoiceData.amount,
            description: `Accounts Payable - ${invoiceData.supplierName || 'Supplier'}`,
            journalEntryId: journalEntry.id,
            accountId: 'acc_2000' // Accounts Payable account
          }
        ]
      });

      // Update processed invoice with journal entry reference
      await prisma.processedInvoice.update({
        where: { id: processedInvoiceId },
        data: { journalEntryId: journalEntry.id }
      });

    } catch (error) {
      console.error('Error creating journal entry from invoice:', error);
      throw new Error('Failed to create journal entry');
    }
  }

  /**
   * Perform OCR extraction using existing pipeline
   */
  private static async performOCRExtraction(filePath: string): Promise<string> {
    try {
      // This would integrate with your existing OCR pipeline
      // For now, return empty string as placeholder
      return '';
    } catch (error) {
      console.error('Error performing OCR extraction:', error);
      return '';
    }
  }

  /**
   * Generate unique journal entry number
   */
  private static async generateJournalEntryNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const count = await prisma.journalEntry.count({
      where: {
        entryNumber: {
          startsWith: `JE-${year}-`
        }
      }
    });
    return `JE-${year}-${String(count + 1).padStart(4, '0')}`;
  }

  /**
   * Validate and approve processed invoice
   */
  static async validateProcessedInvoice(
    processedInvoiceId: string,
    validatedData: Partial<InvoiceExtractionResult>,
    userId: string
  ): Promise<void> {
    try {
      // Update the processed invoice with validated data
      const processedInvoice = await prisma.processedInvoice.findUnique({
        where: { id: processedInvoiceId }
      });

      if (!processedInvoice) {
        throw new Error('Processed invoice not found');
      }

      const originalData = JSON.parse(processedInvoice.extractionResult as string);
      const mergedData = { ...originalData, ...validatedData };

      await prisma.processedInvoice.update({
        where: { id: processedInvoiceId },
        data: {
          extractionResult: JSON.stringify(mergedData),
          status: 'PROCESSED',
          validatedAt: new Date(),
          validatedBy: userId
        }
      });

      // Create journal entry if not already created
      if (!processedInvoice.journalEntryId && mergedData.amount) {
        await this.createJournalEntryFromInvoice(mergedData, userId, processedInvoiceId);
      }

    } catch (error) {
      console.error('Error validating processed invoice:', error);
      throw new Error('Failed to validate processed invoice');
    }
  }

  /**
   * Get processed invoices for review
   */
  static async getProcessedInvoicesForReview(userId: string): Promise<ProcessedInvoiceData[]> {
    try {
      const processedInvoices = await prisma.processedInvoice.findMany({
        where: {
          email: { userId },
          status: { in: ['REQUIRES_VALIDATION', 'PENDING'] }
        },
        include: {
          email: true,
          attachment: true
        },
        orderBy: {
          processedAt: 'desc'
        }
      });

      return processedInvoices.map(pi => ({
        id: pi.id,
        emailId: pi.emailId,
        attachmentId: pi.attachmentId,
        extractionResult: JSON.parse(pi.extractionResult as string),
        status: pi.status as any,
        processedAt: pi.processedAt,
        validatedAt: pi.validatedAt || undefined,
        validatedBy: pi.validatedBy || undefined,
        journalEntryId: pi.journalEntryId || undefined
      }));
    } catch (error) {
      console.error('Error getting processed invoices for review:', error);
      throw new Error('Failed to get processed invoices for review');
    }
  }
}

/**
 * FAZA 3A: Enhanced Inventory Management Service
 * Building on existing inventory foundation for complete ERP functionality
 * Includes Purchase Orders, Supplier Integration, and Cost Tracking
 */

import { prisma } from "~/db.server";
import { gobackendClient } from "~/lib/gobackend-client";

// =====================================================
// TYPES & INTERFACES
// =====================================================

export interface PurchaseOrder {
  id: string;
  orderNumber: string;
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'ORDERED' | 'RECEIVED' | 'CANCELLED';
  orderDate: Date;
  expectedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  subtotal: number;
  taxAmount: number;
  shippingCost: number;
  totalAmount: number;
  notes?: string;
  terms?: string;
  deliveryAddress?: string;
  urgencyLevel: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  supplierId: string;
  createdById: string;
  items: PurchaseOrderItem[];
  supplier?: {
    name: string;
    email?: string;
    phone?: string;
  };
}

export interface PurchaseOrderItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  receivedQuantity: number;
  notes?: string;
  partId: string;
  part?: {
    name: string;
    partNumber?: string;
    unitOfMeasure: string;
  };
}

export interface InventoryOptimizationRecommendation {
  partId: string;
  partName: string;
  currentStock: number;
  recommendedAction: 'ORDER_NOW' | 'ORDER_SOON' | 'REDUCE_STOCK' | 'OPTIMIZE_SUPPLIER';
  recommendedQuantity: number;
  reasoning: string;
  urgency: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  potentialSavings: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
}

// =====================================================
// ENHANCED INVENTORY SERVICE
// =====================================================

export class EnhancedInventoryService {

  /**
   * Generate automated purchase orders based on reorder points
   */
  static async generateAutomatedPurchaseOrders(userId: string): Promise<PurchaseOrder[]> {
    try {
      // Get parts that need reordering
      const partsNeedingReorder = await prisma.inventoryPart.findMany({
        where: {
          isActive: true,
          currentStock: {
            lte: prisma.inventoryPart.fields.reorderPoint
          }
        },
        include: {
          supplier: true
        }
      });

      // Group by supplier
      const supplierGroups = partsNeedingReorder.reduce((groups, part) => {
        const supplierId = part.supplierId || 'no-supplier';
        if (!groups[supplierId]) {
          groups[supplierId] = [];
        }
        groups[supplierId].push(part);
        return groups;
      }, {} as Record<string, typeof partsNeedingReorder>);

      const purchaseOrders: PurchaseOrder[] = [];

      // Create PO for each supplier
      for (const [supplierId, parts] of Object.entries(supplierGroups)) {
        if (supplierId === 'no-supplier') continue;

        const orderNumber = await this.generatePONumber();
        let subtotal = 0;

        // Calculate recommended quantities and costs
        const items: Omit<PurchaseOrderItem, 'id'>[] = [];

        for (const part of parts) {
          const recommendedQuantity = await this.calculateRecommendedOrderQuantity(part.id);
          const unitPrice = part.costPrice || 0;
          const totalPrice = recommendedQuantity * unitPrice;

          items.push({
            quantity: recommendedQuantity,
            unitPrice,
            totalPrice,
            receivedQuantity: 0,
            partId: part.id,
            part: {
              name: part.name,
              partNumber: part.partNumber || undefined,
              unitOfMeasure: part.unitOfMeasure
            }
          });

          subtotal += totalPrice;
        }

        const taxAmount = subtotal * 0.1; // 10% tax rate
        const totalAmount = subtotal + taxAmount;

        // Create the purchase order
        const po = await prisma.purchaseOrder.create({
          data: {
            orderNumber,
            status: 'DRAFT',
            orderDate: new Date(),
            subtotal,
            taxAmount,
            totalAmount,
            urgencyLevel: this.determineUrgencyLevel(parts),
            supplierId,
            createdById: userId,
            items: {
              create: items.map(item => ({
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                receivedQuantity: 0,
                partId: item.partId
              }))
            }
          },
          include: {
            items: {
              include: {
                part: true
              }
            },
            supplier: true
          }
        });

        purchaseOrders.push(po as PurchaseOrder);
      }

      return purchaseOrders;
    } catch (error) {
      console.error('Error generating automated purchase orders:', error);
      throw new Error('Failed to generate purchase orders');
    }
  }

  /**
   * Create manual purchase order
   */
  static async createPurchaseOrder(data: {
    supplierId: string;
    items: Array<{
      partId: string;
      quantity: number;
      unitPrice: number;
    }>;
    notes?: string;
    expectedDeliveryDate?: Date;
    urgencyLevel?: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  }, userId: string): Promise<PurchaseOrder> {
    try {
      const orderNumber = await this.generatePONumber();

      // Calculate totals
      let subtotal = 0;
      const processedItems = data.items.map(item => {
        const totalPrice = item.quantity * item.unitPrice;
        subtotal += totalPrice;
        return {
          ...item,
          totalPrice,
          receivedQuantity: 0
        };
      });

      const taxAmount = subtotal * 0.1; // 10% tax rate
      const totalAmount = subtotal + taxAmount;

      const po = await prisma.purchaseOrder.create({
        data: {
          orderNumber,
          status: 'DRAFT',
          orderDate: new Date(),
          expectedDeliveryDate: data.expectedDeliveryDate,
          subtotal,
          taxAmount,
          totalAmount,
          notes: data.notes,
          urgencyLevel: data.urgencyLevel || 'NORMAL',
          supplierId: data.supplierId,
          createdById: userId,
          items: {
            create: processedItems
          }
        },
        include: {
          items: {
            include: {
              part: true
            }
          },
          supplier: true
        }
      });

      return po as PurchaseOrder;
    } catch (error) {
      console.error('Error creating purchase order:', error);
      throw new Error('Failed to create purchase order');
    }
  }

  /**
   * Approve purchase order
   */
  static async approvePurchaseOrder(poId: string, userId: string): Promise<void> {
    try {
      await prisma.purchaseOrder.update({
        where: { id: poId },
        data: {
          status: 'APPROVED',
          approvedBy: userId,
          approvedAt: new Date()
        }
      });

      // Create approval record
      await prisma.purchaseOrderApproval.create({
        data: {
          status: 'APPROVED',
          purchaseOrderId: poId,
          approverId: userId,
          approvedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error approving purchase order:', error);
      throw new Error('Failed to approve purchase order');
    }
  }

  /**
   * Receive purchase order items
   */
  static async receivePurchaseOrderItems(poId: string, items: Array<{
    itemId: string;
    receivedQuantity: number;
  }>, userId: string): Promise<void> {
    try {
      await prisma.$transaction(async (tx) => {
        // Update received quantities
        for (const item of items) {
          await tx.purchaseOrderItem.update({
            where: { id: item.itemId },
            data: { receivedQuantity: item.receivedQuantity }
          });

          // Get the item details to update inventory
          const poItem = await tx.purchaseOrderItem.findUnique({
            where: { id: item.itemId },
            include: { part: true }
          });

          if (poItem && item.receivedQuantity > 0) {
            // Update inventory stock
            await tx.inventoryPart.update({
              where: { id: poItem.partId },
              data: {
                currentStock: {
                  increment: item.receivedQuantity
                }
              }
            });

            // Create inventory transaction
            await tx.inventoryTransaction.create({
              data: {
                type: 'RECEIPT',
                quantity: item.receivedQuantity,
                unitPrice: poItem.unitPrice,
                totalPrice: item.receivedQuantity * poItem.unitPrice,
                reference: `PO-${poId}`,
                referenceType: 'PURCHASE_ORDER',
                notes: `Received from purchase order`,
                partId: poItem.partId,
                performedById: userId
              }
            });
          }
        }

        // Check if PO is fully received
        const po = await tx.purchaseOrder.findUnique({
          where: { id: poId },
          include: { items: true }
        });

        if (po) {
          const allItemsReceived = po.items.every(item =>
            item.receivedQuantity >= item.quantity
          );

          if (allItemsReceived) {
            await tx.purchaseOrder.update({
              where: { id: poId },
              data: {
                status: 'RECEIVED',
                actualDeliveryDate: new Date()
              }
            });
          }
        }
      });
    } catch (error) {
      console.error('Error receiving purchase order items:', error);
      throw new Error('Failed to receive purchase order items');
    }
  }

  /**
   * Get inventory optimization recommendations using AI
   */
  static async getInventoryOptimizationRecommendations(): Promise<InventoryOptimizationRecommendation[]> {
    try {
      // Get inventory data with usage patterns
      const parts = await prisma.inventoryPart.findMany({
        where: { isActive: true },
        include: {
          supplier: true,
          inventoryTransactions: {
            where: {
              createdAt: {
                gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) // Last 90 days
              }
            },
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      const recommendations: InventoryOptimizationRecommendation[] = [];

      for (const part of parts) {
        const recommendation = await this.analyzePartOptimization(part);
        if (recommendation) {
          recommendations.push(recommendation);
        }
      }

      // Sort by urgency and potential savings
      return recommendations.sort((a, b) => {
        const urgencyOrder = { 'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
        const urgencyDiff = urgencyOrder[b.urgency] - urgencyOrder[a.urgency];
        if (urgencyDiff !== 0) return urgencyDiff;
        return b.potentialSavings - a.potentialSavings;
      });
    } catch (error) {
      console.error('Error getting optimization recommendations:', error);
      throw new Error('Failed to get optimization recommendations');
    }
  }

  /**
   * Get purchase order list with filters
   */
  static async getPurchaseOrders(filters?: {
    status?: string;
    supplierId?: string;
    dateFrom?: Date;
    dateTo?: Date;
    limit?: number;
    offset?: number;
  }): Promise<PurchaseOrder[]> {
    try {
      const where: any = {};

      if (filters?.status) {
        where.status = filters.status;
      }

      if (filters?.supplierId) {
        where.supplierId = filters.supplierId;
      }

      if (filters?.dateFrom || filters?.dateTo) {
        where.orderDate = {};
        if (filters.dateFrom) where.orderDate.gte = filters.dateFrom;
        if (filters.dateTo) where.orderDate.lte = filters.dateTo;
      }

      const purchaseOrders = await prisma.purchaseOrder.findMany({
        where,
        include: {
          items: {
            include: {
              part: true
            }
          },
          supplier: true
        },
        orderBy: { orderDate: 'desc' },
        take: filters?.limit || 50,
        skip: filters?.offset || 0
      });

      return purchaseOrders as PurchaseOrder[];
    } catch (error) {
      console.error('Error getting purchase orders:', error);
      throw new Error('Failed to get purchase orders');
    }
  }

  /**
   * Get purchase order by ID
   */
  static async getPurchaseOrder(id: string): Promise<PurchaseOrder | null> {
    try {
      const po = await prisma.purchaseOrder.findUnique({
        where: { id },
        include: {
          items: {
            include: {
              part: true
            }
          },
          supplier: true
        }
      });

      return po as PurchaseOrder | null;
    } catch (error) {
      console.error('Error getting purchase order:', error);
      throw new Error('Failed to get purchase order');
    }
  }

  // =====================================================
  // PRIVATE HELPER METHODS
  // =====================================================

  private static async generatePONumber(): Promise<string> {
    const year = new Date().getFullYear();
    const count = await prisma.purchaseOrder.count({
      where: {
        orderNumber: {
          startsWith: `PO-${year}-`
        }
      }
    });
    return `PO-${year}-${String(count + 1).padStart(4, '0')}`;
  }

  private static async calculateRecommendedOrderQuantity(partId: string): Promise<number> {
    // Get usage data for the last 90 days
    const transactions = await prisma.inventoryTransaction.findMany({
      where: {
        partId,
        type: 'USAGE',
        createdAt: {
          gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
        }
      }
    });

    const totalUsage = transactions.reduce((sum, t) => sum + Math.abs(t.quantity), 0);
    const averageDailyUsage = totalUsage / 90;

    // Calculate recommended quantity: 60 days supply + safety stock
    const recommendedQuantity = Math.ceil(averageDailyUsage * 60 * 1.2); // 20% safety stock

    return Math.max(recommendedQuantity, 1);
  }

  private static determineUrgencyLevel(parts: any[]): 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT' {
    const criticalParts = parts.filter(p => p.currentStock <= 0);
    const lowStockParts = parts.filter(p => p.currentStock <= p.reorderPoint * 0.5);

    if (criticalParts.length > 0) return 'URGENT';
    if (lowStockParts.length > parts.length * 0.5) return 'HIGH';
    if (lowStockParts.length > 0) return 'NORMAL';
    return 'LOW';
  }

  private static async analyzePartOptimization(part: any): Promise<InventoryOptimizationRecommendation | null> {
    const usageTransactions = part.inventoryTransactions.filter((t: any) => t.type === 'USAGE');
    const totalUsage = usageTransactions.reduce((sum: number, t: any) => sum + Math.abs(t.quantity), 0);
    const averageDailyUsage = totalUsage / 90;

    // Determine recommendation based on stock levels and usage
    if (part.currentStock <= part.reorderPoint) {
      const recommendedQuantity = Math.ceil(averageDailyUsage * 60);
      return {
        partId: part.id,
        partName: part.name,
        currentStock: part.currentStock,
        recommendedAction: part.currentStock <= 0 ? 'ORDER_NOW' : 'ORDER_SOON',
        recommendedQuantity,
        reasoning: part.currentStock <= 0
          ? 'Stock depleted - immediate reorder required'
          : 'Stock below reorder point - order soon to avoid stockout',
        urgency: part.currentStock <= 0 ? 'CRITICAL' : 'HIGH',
        potentialSavings: 0,
        riskLevel: part.currentStock <= 0 ? 'HIGH' : 'MEDIUM'
      };
    }

    return null;
  }
}

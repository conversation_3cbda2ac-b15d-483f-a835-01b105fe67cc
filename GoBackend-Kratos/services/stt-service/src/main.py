#!/usr/bin/env python3
"""
🎤 HVAC STT Service - Main Application
Dual-Engine Speech-to-Text Service for HVAC CRM System

Primary: NVIDIA NeMo FastConformer (Local)
Backup: ElevenLabs Scribe (Cloud API)
"""

import asyncio
import logging
import os
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import structlog

# Import our STT engines
from engines.nemo_engine import NeMoSTTEngine
from engines.elevenlabs_engine import ElevenLabsSTTEngine
from engines.dual_engine_coordinator import DualEngineCoordinator
from models.stt_models import (
    TranscriptionRequest,
    TranscriptionResponse,
    HealthResponse,
    STTEngineStatus
)
from utils.audio_processor import AudioProcessor
from utils.config_manager import ConfigManager
from utils.metrics_collector import MetricsCollector

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Global instances
config_manager: ConfigManager = None
dual_coordinator: DualEngineCoordinator = None
audio_processor: AudioProcessor = None
metrics_collector: MetricsCollector = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global config_manager, dual_coordinator, audio_processor, metrics_collector
    
    logger.info("🚀 Starting HVAC STT Service...")
    
    try:
        # Initialize configuration
        config_manager = ConfigManager()
        await config_manager.load_config()
        
        # Initialize audio processor
        audio_processor = AudioProcessor(config_manager.get_audio_config())
        
        # Initialize metrics collector
        metrics_collector = MetricsCollector()
        
        # Initialize STT engines
        nemo_engine = NeMoSTTEngine(config_manager.get_nemo_config())
        elevenlabs_engine = ElevenLabsSTTEngine(config_manager.get_elevenlabs_config())
        
        # Initialize dual engine coordinator
        dual_coordinator = DualEngineCoordinator(
            primary_engine=nemo_engine,
            backup_engine=elevenlabs_engine,
            config=config_manager.get_coordinator_config(),
            metrics=metrics_collector
        )
        
        # Initialize engines
        await dual_coordinator.initialize()
        
        logger.info("✅ HVAC STT Service initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error("❌ Failed to initialize STT service", error=str(e))
        raise
    finally:
        # Cleanup
        logger.info("🛑 Shutting down HVAC STT Service...")
        if dual_coordinator:
            await dual_coordinator.shutdown()
        logger.info("✅ STT Service shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="HVAC STT Service",
    description="Dual-Engine Speech-to-Text Service for HVAC CRM",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        if not dual_coordinator:
            raise HTTPException(status_code=503, detail="Service not initialized")
        
        # Get engine statuses
        nemo_status = await dual_coordinator.get_primary_engine_status()
        elevenlabs_status = await dual_coordinator.get_backup_engine_status()
        
        # Get system metrics
        system_metrics = metrics_collector.get_system_metrics() if metrics_collector else {}
        
        return HealthResponse(
            status="healthy",
            timestamp=metrics_collector.get_current_timestamp(),
            engines={
                "nemo": nemo_status,
                "elevenlabs": elevenlabs_status
            },
            system_metrics=system_metrics,
            version="1.0.0"
        )
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail=f"Health check failed: {str(e)}")


@app.post("/transcribe", response_model=TranscriptionResponse)
async def transcribe_audio(
    background_tasks: BackgroundTasks,
    audio_file: UploadFile = File(...),
    language: str = "pl",
    use_primary_only: bool = False,
    include_timestamps: bool = True,
    include_confidence: bool = True
):
    """
    Transcribe audio file using dual-engine STT system
    
    Args:
        audio_file: Audio file (M4A, MP3, WAV supported)
        language: Language code (default: 'pl' for Polish)
        use_primary_only: Force use of primary engine only
        include_timestamps: Include word-level timestamps
        include_confidence: Include confidence scores
    """
    if not dual_coordinator:
        raise HTTPException(status_code=503, detail="STT service not initialized")
    
    logger.info(
        "🎤 Transcription request received",
        filename=audio_file.filename,
        content_type=audio_file.content_type,
        language=language
    )
    
    try:
        # Read audio file
        audio_data = await audio_file.read()
        
        # Validate audio file
        if not audio_processor.validate_audio_file(audio_data, audio_file.filename):
            raise HTTPException(status_code=400, detail="Invalid audio file format")
        
        # Process audio if needed
        processed_audio = await audio_processor.process_audio(
            audio_data, 
            audio_file.filename
        )
        
        # Create transcription request
        request = TranscriptionRequest(
            audio_data=processed_audio,
            filename=audio_file.filename,
            language=language,
            use_primary_only=use_primary_only,
            include_timestamps=include_timestamps,
            include_confidence=include_confidence
        )
        
        # Perform transcription
        result = await dual_coordinator.transcribe(request)
        
        # Log metrics in background
        background_tasks.add_task(
            metrics_collector.record_transcription,
            result.engine_used,
            result.processing_time,
            result.success,
            len(audio_data)
        )
        
        logger.info(
            "✅ Transcription completed",
            filename=audio_file.filename,
            engine_used=result.engine_used,
            processing_time=result.processing_time,
            success=result.success
        )
        
        return result
        
    except Exception as e:
        logger.error(
            "❌ Transcription failed",
            filename=audio_file.filename,
            error=str(e)
        )
        
        # Record failure metrics
        if metrics_collector:
            background_tasks.add_task(
                metrics_collector.record_transcription,
                "unknown",
                0.0,
                False,
                len(audio_data) if 'audio_data' in locals() else 0
            )
        
        raise HTTPException(status_code=500, detail=f"Transcription failed: {str(e)}")


@app.get("/metrics")
async def get_metrics():
    """Get service metrics"""
    if not metrics_collector:
        raise HTTPException(status_code=503, detail="Metrics not available")
    
    return metrics_collector.get_all_metrics()


@app.get("/engines/status")
async def get_engines_status():
    """Get detailed status of both STT engines"""
    if not dual_coordinator:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    try:
        return {
            "primary": await dual_coordinator.get_primary_engine_status(),
            "backup": await dual_coordinator.get_backup_engine_status(),
            "coordinator": await dual_coordinator.get_coordinator_status()
        }
    except Exception as e:
        logger.error("Failed to get engines status", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")


@app.post("/engines/switch")
async def switch_primary_engine(engine: str):
    """Switch primary engine (for testing/maintenance)"""
    if not dual_coordinator:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    if engine not in ["nemo", "elevenlabs"]:
        raise HTTPException(status_code=400, detail="Invalid engine name")
    
    try:
        await dual_coordinator.switch_primary_engine(engine)
        return {"message": f"Primary engine switched to {engine}"}
    except Exception as e:
        logger.error("Failed to switch engine", engine=engine, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to switch engine: {str(e)}")


if __name__ == "__main__":
    # Get configuration from environment
    host = os.getenv("STT_SERVICE_HOST", "0.0.0.0")
    port = int(os.getenv("STT_SERVICE_PORT", "8085"))
    log_level = os.getenv("LOG_LEVEL", "INFO").lower()
    
    # Configure uvicorn logging
    logging.basicConfig(level=getattr(logging, log_level.upper()))
    
    logger.info(
        "🎤 Starting HVAC STT Service",
        host=host,
        port=port,
        log_level=log_level
    )
    
    # Run the application
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        log_level=log_level,
        reload=False,
        workers=1  # Single worker for GPU usage
    )

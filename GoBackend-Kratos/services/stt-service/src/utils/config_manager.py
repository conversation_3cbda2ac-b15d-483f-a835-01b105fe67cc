"""
⚙️ Configuration Manager
Manages configuration for the STT service
"""

import os
import yaml
from typing import Dict, Any, Optional
import structlog

from models.stt_models import (
    CoordinatorConfig,
    NeMoConfig,
    ElevenLabsConfig,
    AudioConfig,
    STTEngineType
)

logger = structlog.get_logger(__name__)


class ConfigManager:
    """Configuration manager for STT service"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or os.getenv("STT_CONFIG_PATH", "config/stt_config.yaml")
        self.config_data = {}
        
        logger.info("⚙️ Configuration Manager initialized", config_path=self.config_path)
    
    async def load_config(self):
        """Load configuration from file and environment"""
        try:
            # Load from YAML file if exists
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    self.config_data = yaml.safe_load(f) or {}
                logger.info("📄 Configuration loaded from file", path=self.config_path)
            else:
                logger.info("📄 Configuration file not found, using defaults and environment")
                self.config_data = {}
            
            # Override with environment variables
            self._load_from_environment()
            
            logger.info("✅ Configuration loaded successfully")
            
        except Exception as e:
            logger.error("❌ Failed to load configuration", error=str(e))
            raise
    
    def _load_from_environment(self):
        """Load configuration from environment variables"""
        # NeMo configuration
        nemo_model_path = os.getenv("NEMO_MODEL_PATH")
        if nemo_model_path:
            if "nemo" not in self.config_data:
                self.config_data["nemo"] = {}
            self.config_data["nemo"]["model_path"] = nemo_model_path
        
        # ElevenLabs configuration
        elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")
        if elevenlabs_api_key:
            if "elevenlabs" not in self.config_data:
                self.config_data["elevenlabs"] = {}
            self.config_data["elevenlabs"]["api_key"] = elevenlabs_api_key
        
        # Service configuration
        service_config = {
            "host": os.getenv("STT_SERVICE_HOST", "0.0.0.0"),
            "port": int(os.getenv("STT_SERVICE_PORT", "8085")),
            "log_level": os.getenv("LOG_LEVEL", "INFO")
        }
        self.config_data["service"] = service_config
    
    def get_coordinator_config(self) -> CoordinatorConfig:
        """Get coordinator configuration"""
        coordinator_data = self.config_data.get("coordinator", {})
        
        return CoordinatorConfig(
            primary_engine=STTEngineType(coordinator_data.get("primary_engine", "nemo")),
            fallback_enabled=coordinator_data.get("fallback_enabled", True),
            fallback_threshold=coordinator_data.get("fallback_threshold", 0.5),
            max_retries=coordinator_data.get("max_retries", 2),
            timeout_seconds=coordinator_data.get("timeout_seconds", 30.0),
            health_check_interval=coordinator_data.get("health_check_interval", 60)
        )
    
    def get_nemo_config(self) -> NeMoConfig:
        """Get NeMo configuration"""
        nemo_data = self.config_data.get("nemo", {})
        
        # Default model path
        default_model_path = os.getenv(
            "NEMO_MODEL_PATH",
            "/app/models/stt_pl_fastconformer_hybrid_large_pc.nemo"
        )
        
        return NeMoConfig(
            model_path=nemo_data.get("model_path", default_model_path),
            device=nemo_data.get("device", "cuda" if os.getenv("CUDA_VISIBLE_DEVICES") else "cpu"),
            batch_size=nemo_data.get("batch_size", 1),
            beam_size=nemo_data.get("beam_size", 1),
            return_timestamps=nemo_data.get("return_timestamps", True),
            preserve_alignment=nemo_data.get("preserve_alignment", True)
        )
    
    def get_elevenlabs_config(self) -> ElevenLabsConfig:
        """Get ElevenLabs configuration"""
        elevenlabs_data = self.config_data.get("elevenlabs", {})
        
        api_key = elevenlabs_data.get("api_key") or os.getenv("ELEVENLABS_API_KEY")
        if not api_key:
            logger.warning("⚠️ ElevenLabs API key not configured")
            api_key = "dummy_key"  # Will cause initialization to fail gracefully
        
        return ElevenLabsConfig(
            api_key=api_key,
            api_url=elevenlabs_data.get("api_url", "https://api.elevenlabs.io/v1/speech-to-text"),
            timeout=elevenlabs_data.get("timeout", 60.0),
            max_file_size=elevenlabs_data.get("max_file_size", 25 * 1024 * 1024),
            supported_formats=elevenlabs_data.get("supported_formats", ["m4a", "mp3", "wav"])
        )
    
    def get_audio_config(self) -> AudioConfig:
        """Get audio processing configuration"""
        audio_data = self.config_data.get("audio", {})
        
        return AudioConfig(
            max_file_size=audio_data.get("max_file_size", 50 * 1024 * 1024),
            target_sample_rate=audio_data.get("target_sample_rate", 16000),
            normalize_audio=audio_data.get("normalize_audio", True),
            remove_silence=audio_data.get("remove_silence", False),
            temp_dir=audio_data.get("temp_dir", "/tmp/stt")
        )
    
    def get_service_config(self) -> Dict[str, Any]:
        """Get service configuration"""
        return self.config_data.get("service", {
            "host": "0.0.0.0",
            "port": 8085,
            "log_level": "INFO"
        })
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key"""
        keys = key.split('.')
        value = self.config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set_config_value(self, key: str, value: Any):
        """Set configuration value by key"""
        keys = key.split('.')
        config = self.config_data
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    async def save_config(self):
        """Save configuration to file"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w') as f:
                yaml.dump(self.config_data, f, default_flow_style=False)
            
            logger.info("💾 Configuration saved", path=self.config_path)
            
        except Exception as e:
            logger.error("❌ Failed to save configuration", error=str(e))
            raise

"""
🎤 ElevenLabs STT Engine
Backup STT engine using ElevenLabs Scribe API for high-accuracy transcription
"""

import asyncio
import time
from typing import Optional, Dict, Any, List
import json

import httpx
import structlog

from models.stt_models import (
    TranscriptionRequest,
    TranscriptionResponse,
    TranscriptionMetadata,
    WordTimestamp,
    SpeakerSegment,
    STTEngineType,
    STTEngineStatus,
    EngineHealthStatus,
    ElevenLabsConfig
)

logger = structlog.get_logger(__name__)


class ElevenLabsSTTEngine:
    """ElevenLabs Scribe STT Engine"""
    
    def __init__(self, config: ElevenLabsConfig):
        self.config = config
        self.client = None
        self.is_initialized = False
        self.last_used = None
        self.total_requests = 0
        self.successful_requests = 0
        self.total_processing_time = 0.0
        self.last_error = None
        
        logger.info(
            "🌐 Initializing ElevenLabs STT Engine",
            api_url=config.api_url
        )
    
    async def initialize(self) -> bool:
        """Initialize the ElevenLabs client"""
        try:
            logger.info("🔄 Initializing ElevenLabs client...")
            
            # Create HTTP client with timeout
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.config.timeout),
                headers={
                    "xi-api-key": self.config.api_key,
                    "Content-Type": "multipart/form-data"
                }
            )
            
            # Test API connectivity
            await self._test_api_connection()
            
            self.is_initialized = True
            logger.info("✅ ElevenLabs client initialized successfully")
            return True
            
        except Exception as e:
            self.last_error = str(e)
            logger.error("❌ Failed to initialize ElevenLabs client", error=str(e))
            return False
    
    async def _test_api_connection(self):
        """Test API connection"""
        try:
            # Make a simple request to test connectivity
            # Note: This is a placeholder - adjust based on actual ElevenLabs API
            response = await self.client.get("https://api.elevenlabs.io/v1/user")
            if response.status_code not in [200, 401]:  # 401 is OK for testing connectivity
                raise Exception(f"API test failed with status {response.status_code}")
        except httpx.RequestError as e:
            raise Exception(f"API connection test failed: {str(e)}")
    
    async def transcribe(self, request: TranscriptionRequest) -> TranscriptionResponse:
        """Transcribe audio using ElevenLabs Scribe API"""
        if not self.is_initialized:
            raise RuntimeError("ElevenLabs engine not initialized")
        
        start_time = time.time()
        self.total_requests += 1
        self.last_used = time.time()
        
        try:
            logger.info(
                "🌐 Starting ElevenLabs transcription",
                filename=request.filename,
                language=request.language,
                file_size=len(request.audio_data)
            )
            
            # Validate file size
            if len(request.audio_data) > self.config.max_file_size:
                raise ValueError(f"File too large: {len(request.audio_data)} bytes")
            
            # Prepare the request
            files = {
                "audio": (request.filename, request.audio_data, "audio/m4a")
            }
            
            data = {
                "model": "scribe_v1",
                "language": request.language,
                "response_format": "verbose_json"
            }
            
            # Add optional parameters
            if request.include_timestamps:
                data["timestamp_granularities"] = ["word", "segment"]
            
            # Make API request
            response = await self.client.post(
                self.config.api_url,
                files=files,
                data=data
            )
            
            if response.status_code != 200:
                raise Exception(f"API request failed: {response.status_code} - {response.text}")
            
            result = response.json()
            
            # Parse the response
            transcript = result.get("text", "")
            confidence = result.get("confidence", 0.8)  # ElevenLabs typically has high confidence
            
            # Extract word-level timestamps
            words = []
            if request.include_timestamps and "words" in result:
                words = self._extract_word_timestamps(result["words"])
            
            # Extract speaker segments if available
            speakers = []
            if "segments" in result:
                speakers = self._extract_speaker_segments(result["segments"])
            
            processing_time = time.time() - start_time
            self.total_processing_time += processing_time
            self.successful_requests += 1
            
            # Get audio metadata from response
            audio_duration = result.get("duration", None)
            language_detected = result.get("language", request.language)
            
            metadata = TranscriptionMetadata(
                engine_used=STTEngineType.ELEVENLABS,
                processing_time=processing_time,
                audio_duration=audio_duration,
                file_size=len(request.audio_data),
                language_detected=language_detected,
                quality_score=confidence
            )
            
            logger.info(
                "✅ ElevenLabs transcription completed",
                filename=request.filename,
                processing_time=processing_time,
                transcript_length=len(transcript),
                confidence=confidence
            )
            
            return TranscriptionResponse(
                success=True,
                transcript=transcript,
                confidence=confidence,
                words=words if words else None,
                speakers=speakers if speakers else None,
                metadata=metadata
            )
        
        except Exception as e:
            processing_time = time.time() - start_time
            self.last_error = str(e)
            
            logger.error(
                "❌ ElevenLabs transcription failed",
                filename=request.filename,
                error=str(e),
                processing_time=processing_time
            )
            
            metadata = TranscriptionMetadata(
                engine_used=STTEngineType.ELEVENLABS,
                processing_time=processing_time,
                file_size=len(request.audio_data)
            )
            
            return TranscriptionResponse(
                success=False,
                transcript="",
                error=str(e),
                metadata=metadata
            )
    
    def _extract_word_timestamps(self, words_data: List[Dict]) -> List[WordTimestamp]:
        """Extract word-level timestamps from ElevenLabs response"""
        words = []
        
        try:
            for word_info in words_data:
                words.append(WordTimestamp(
                    word=word_info.get("word", ""),
                    start_time=word_info.get("start", 0.0),
                    end_time=word_info.get("end", 0.0),
                    confidence=word_info.get("confidence", None)
                ))
        except Exception as e:
            logger.warning("Failed to extract word timestamps", error=str(e))
        
        return words
    
    def _extract_speaker_segments(self, segments_data: List[Dict]) -> List[SpeakerSegment]:
        """Extract speaker segments from ElevenLabs response"""
        speakers = []
        
        try:
            for segment_info in segments_data:
                speakers.append(SpeakerSegment(
                    speaker_id=segment_info.get("speaker", "unknown"),
                    start_time=segment_info.get("start", 0.0),
                    end_time=segment_info.get("end", 0.0),
                    text=segment_info.get("text", ""),
                    confidence=segment_info.get("confidence", None)
                ))
        except Exception as e:
            logger.warning("Failed to extract speaker segments", error=str(e))
        
        return speakers
    
    async def get_status(self) -> EngineHealthStatus:
        """Get engine health status"""
        if not self.is_initialized:
            status = STTEngineStatus.UNAVAILABLE
            available = False
        else:
            # Test API availability
            try:
                await self._test_api_connection()
                status = STTEngineStatus.HEALTHY
                available = True
            except Exception as e:
                status = STTEngineStatus.DEGRADED
                available = False
                self.last_error = str(e)
        
        error_rate = 0.0
        if self.total_requests > 0:
            error_rate = ((self.total_requests - self.successful_requests) / self.total_requests) * 100
        
        avg_processing_time = None
        if self.successful_requests > 0:
            avg_processing_time = self.total_processing_time / self.successful_requests
        
        return EngineHealthStatus(
            status=status,
            available=available,
            last_used=self.last_used,
            total_requests=self.total_requests,
            successful_requests=self.successful_requests,
            average_processing_time=avg_processing_time,
            error_rate=error_rate,
            last_error=self.last_error
        )
    
    async def shutdown(self):
        """Shutdown the engine"""
        logger.info("🛑 Shutting down ElevenLabs engine...")
        
        if self.client:
            await self.client.aclose()
            self.client = None
        
        self.is_initialized = False
        logger.info("✅ ElevenLabs engine shutdown complete")
    
    def is_available(self) -> bool:
        """Check if engine is available"""
        return self.is_initialized and self.client is not None

# 🎤 HVAC STT Service Environment Configuration
# Copy this file to .env and configure your settings

# =============================================================================
# SERVICE CONFIGURATION
# =============================================================================

# Service host and port
STT_SERVICE_HOST=0.0.0.0
STT_SERVICE_PORT=8085

# Logging level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# =============================================================================
# NVIDIA NEMO CONFIGURATION (Primary Engine)
# =============================================================================

# Path to NeMo model file (inside container)
NEMO_MODEL_PATH=/app/models/stt_pl_fastconformer_hybrid_large_pc.nemo

# GPU device to use (0 for first GPU, -1 for CPU)
CUDA_VISIBLE_DEVICES=0

# =============================================================================
# ELEVENLABS CONFIGURATION (Backup Engine)
# =============================================================================

# ElevenLabs API key (REQUIRED for backup engine)
# Get your API key from: https://elevenlabs.io/app/speech-synthesis
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# ElevenLabs API URL (usually no need to change)
ELEVENLABS_API_URL=https://api.elevenlabs.io/v1/speech-to-text

# =============================================================================
# INTEGRATION URLS
# =============================================================================

# HVAC Backend URL for integration
HVAC_BACKEND_URL=http://hvac-backend:8080

# Email Intelligence Service URL
EMAIL_SERVICE_URL=http://email-intelligence:8082

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Maximum concurrent transcription requests
MAX_CONCURRENT_REQUESTS=5

# Request timeout in seconds
REQUEST_TIMEOUT=60

# Maximum audio file size in bytes (50MB)
MAX_FILE_SIZE=52428800

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Enable Prometheus metrics export
PROMETHEUS_ENABLED=false

# Metrics collection interval in seconds
METRICS_INTERVAL=30

# Health check interval in seconds
HEALTH_CHECK_INTERVAL=60

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Enable debug mode (more verbose logging)
DEBUG_MODE=false

# Enable hot reload for development
HOT_RELOAD=false

# Mock engines for testing (bypasses actual STT)
MOCK_ENGINES=false

# Test mode (uses dummy responses)
TEST_MODE=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Enable CORS (for web interface access)
CORS_ENABLED=true

# Allowed CORS origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Enable rate limiting
RATE_LIMITING_ENABLED=false

# Requests per minute limit
RATE_LIMIT_RPM=60

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker network name
DOCKER_NETWORK=hvac-network

# Container restart policy
RESTART_POLICY=unless-stopped

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Fallback threshold (confidence score below which to use backup)
FALLBACK_THRESHOLD=0.5

# Enable automatic fallback to ElevenLabs
FALLBACK_ENABLED=true

# Maximum retry attempts
MAX_RETRIES=2

# =============================================================================
# AUDIO PROCESSING CONFIGURATION
# =============================================================================

# Target sample rate for audio processing
TARGET_SAMPLE_RATE=16000

# Enable audio normalization
NORMALIZE_AUDIO=true

# Enable silence removal
REMOVE_SILENCE=false

# Temporary directory for audio processing
TEMP_DIR=/tmp/stt

# =============================================================================
# EXAMPLE CONFIGURATIONS FOR DIFFERENT ENVIRONMENTS
# =============================================================================

# Development Environment
# DEBUG_MODE=true
# LOG_LEVEL=DEBUG
# MOCK_ENGINES=true
# TEST_MODE=true

# Production Environment
# LOG_LEVEL=INFO
# PROMETHEUS_ENABLED=true
# RATE_LIMITING_ENABLED=true
# CORS_ENABLED=false

# High-Performance Environment
# MAX_CONCURRENT_REQUESTS=10
# REQUEST_TIMEOUT=120
# CUDA_VISIBLE_DEVICES=0,1  # Multiple GPUs

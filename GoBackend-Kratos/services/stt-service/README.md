# 🎤 HVAC STT Service

Containerized dual-engine Speech-to-Text service for the HVAC CRM system, providing intelligent transcription of M4A audio attachments from the `<EMAIL>` email account.

## 🎯 Features

### Dual-Engine Architecture
- **Primary Engine**: NVIDIA NeMo FastConformer (Local GPU processing)
- **Backup Engine**: ElevenLabs Scribe API (Cloud fallback)
- **Intelligent Routing**: Automatic fallback with quality thresholds

### Audio Processing
- **Supported Formats**: M4A, MP3, WAV, FLAC
- **Audio Optimization**: Automatic resampling, normalization, silence removal
- **Large File Support**: Up to 50MB audio files

### Advanced Features
- **Word-level Timestamps**: Precise timing information
- **Speaker Diarization**: Multi-speaker identification (ElevenLabs)
- **Confidence Scoring**: Quality assessment for each transcription
- **Polish Language Optimized**: Specialized models for Polish transcription

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Email System  │───▶│   STT Service    │───▶│  HVAC Backend   │
│  (M4A Attachs)  │    │  (Dual Engine)   │    │   (Analysis)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                    ┌─────────┴─────────┐
                    │                   │
            ┌───────▼────────┐  ┌───────▼────────┐
            │ NVIDIA NeMo    │  │ ElevenLabs     │
            │ (Primary)      │  │ (Backup)       │
            │ Local GPU      │  │ Cloud API      │
            └────────────────┘  └────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker with GPU support (NVIDIA Docker)
- NVIDIA GPU with CUDA 12.1+
- ElevenLabs API key (for backup engine)

### 1. Clone and Setup
```bash
cd GoBackend-Kratos/services/stt-service
chmod +x scripts/*.sh
```

### 2. Configure Environment
```bash
# Copy and edit environment file
cp .env.example .env
# Edit .env and add your ElevenLabs API key
```

### 3. Deploy Service
```bash
# Deploy with monitoring (recommended)
./scripts/deploy.sh monitoring

# Or deploy core service only
./scripts/deploy.sh
```

### 4. Verify Deployment
```bash
# Run comprehensive tests
./scripts/test.sh

# Check service status
./scripts/deploy.sh status
```

## 📋 Configuration

### Environment Variables
```bash
# Service Configuration
STT_SERVICE_HOST=0.0.0.0
STT_SERVICE_PORT=8085
LOG_LEVEL=INFO

# NVIDIA NeMo Configuration
NEMO_MODEL_PATH=/app/models/stt_pl_fastconformer_hybrid_large_pc.nemo
CUDA_VISIBLE_DEVICES=0

# ElevenLabs Configuration
ELEVENLABS_API_KEY=your_api_key_here

# Integration URLs
HVAC_BACKEND_URL=http://hvac-backend:8080
EMAIL_SERVICE_URL=http://email-intelligence:8082
```

### Configuration File
See `config/stt_config.yaml` for detailed configuration options.

## 🔌 API Endpoints

### Transcription
```bash
POST /transcribe
Content-Type: multipart/form-data

# Parameters:
# - audio_file: Audio file (M4A, MP3, WAV, FLAC)
# - language: Language code (default: 'pl')
# - include_timestamps: Include word timestamps (default: true)
# - include_confidence: Include confidence scores (default: true)
```

### Health Check
```bash
GET /health
# Returns service and engine health status
```

### Engine Status
```bash
GET /engines/status
# Returns detailed status of both STT engines
```

### Metrics
```bash
GET /metrics
# Returns comprehensive service metrics
```

## 📊 Monitoring

### Health Monitoring
- Automatic health checks every 60 seconds
- Engine availability monitoring
- Performance metrics collection

### Metrics Dashboard
Access Grafana dashboard at `http://localhost:3001` (admin/admin) when deployed with monitoring.

### Key Metrics
- **Transcription Success Rate**: Percentage of successful transcriptions
- **Engine Usage**: Distribution between NeMo and ElevenLabs
- **Processing Time**: Average transcription time per engine
- **System Resources**: CPU, Memory, GPU utilization

## 🧪 Testing

### Basic Tests
```bash
# Run all tests
./scripts/test.sh

# Test specific components
./scripts/test.sh health
./scripts/test.sh engines
./scripts/test.sh metrics
```

### Performance Testing
```bash
# Performance test with multiple requests
./scripts/test.sh performance

# Load test with concurrent requests
./scripts/test.sh load
```

### Manual Testing
```bash
# Test transcription with audio file
curl -X POST \
  -F "audio_file=@test.m4a" \
  -F "language=pl" \
  http://localhost:8085/transcribe
```

## 🔧 Integration with HVAC System

### Email Processing Integration
The STT service integrates with the dual-source email processor:

```go
// In dual_source_processor.go
sttClient := NewSTTServiceClient("http://stt-service:8085", logger)
response, err := sttClient.TranscribeAudio(ctx, audioData, filename, "pl")
```

### Docker Compose Integration
Add to your main `docker-compose.yml`:

```yaml
services:
  stt-service:
    image: hvac-stt-service:latest
    ports:
      - "8085:8085"
    environment:
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

## 📈 Performance Benchmarks

### NVIDIA NeMo (Primary)
- **Accuracy**: ~92-95% for Polish audio
- **Processing Speed**: 2-5x real-time (GPU dependent)
- **Latency**: 2-10 seconds for 1-minute audio
- **Resource Usage**: 2-4GB GPU memory

### ElevenLabs Scribe (Backup)
- **Accuracy**: ~97% for Polish audio (2.3% WER)
- **Processing Speed**: 1-3x real-time
- **Latency**: 5-15 seconds for 1-minute audio
- **Cost**: $0.40 per hour of audio

## 🛠️ Troubleshooting

### Common Issues

#### GPU Not Available
```bash
# Check NVIDIA Docker
docker run --rm --gpus all nvidia/cuda:12.1-base-ubuntu22.04 nvidia-smi

# If failed, install NVIDIA Docker runtime
# https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html
```

#### ElevenLabs API Errors
```bash
# Check API key configuration
docker-compose logs stt-service | grep -i elevenlabs

# Verify API key
curl -H "xi-api-key: YOUR_API_KEY" https://api.elevenlabs.io/v1/user
```

#### Model Download Issues
```bash
# Check model download
docker-compose exec stt-service ls -la /app/models/

# Manual model download
wget -O models/stt_pl_fastconformer_hybrid_large_pc.nemo \
  "https://api.ngc.nvidia.com/v2/models/nvidia/nemo/stt_pl_fastconformer_hybrid_large_pc/versions/1.0.0/files/stt_pl_fastconformer_hybrid_large_pc.nemo"
```

### Logs and Debugging
```bash
# View service logs
docker-compose logs -f stt-service

# Check health status
curl http://localhost:8085/health | jq

# Monitor metrics
curl http://localhost:8085/metrics | jq
```

## 🔄 Updates and Maintenance

### Updating Models
```bash
# Update NeMo model
docker-compose exec stt-service wget -O /app/models/new_model.nemo MODEL_URL

# Restart service
docker-compose restart stt-service
```

### Scaling
```bash
# Scale for higher load (if multiple GPUs available)
docker-compose up -d --scale stt-service=2
```

## 📚 Additional Resources

- [NVIDIA NeMo Documentation](https://docs.nvidia.com/nemo-framework/)
- [ElevenLabs API Documentation](https://elevenlabs.io/docs)
- [HVAC CRM Integration Guide](../../../docs/HVAC_REMIX_INTEGRATION.md)

## 🤝 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review service logs: `docker-compose logs stt-service`
3. Test with the provided test scripts
4. Check system resources and GPU availability

---

**Version**: 1.0.0  
**Last Updated**: 2024  
**Maintainer**: HVAC CRM Team

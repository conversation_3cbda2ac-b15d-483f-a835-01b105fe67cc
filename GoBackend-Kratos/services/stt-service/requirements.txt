# 🎤 HVAC STT Service Dependencies
# Core STT and AI dependencies for dual-engine transcription

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# Audio Processing
librosa==0.10.1
soundfile==0.12.1
pydub==2.8.0
scipy==1.11.4

# NVIDIA NeMo (will be installed in Dockerfile)
# nemo_toolkit[asr]==1.22.0

# PyTorch (will be installed in Dockerfile with CUDA support)
# torch==2.1.0
# torchaudio==2.1.0

# HTTP Client for ElevenLabs API
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Async and Concurrency
asyncio-throttle==1.0.2
aiofiles==23.2.1

# Logging and Monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Configuration
pyyaml==6.0.1
python-dotenv==1.0.0

# Utilities
typing-extensions==4.8.0
python-dateutil==2.8.2
pytz==2023.3

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Health Checks and Metrics
psutil==5.9.6

# 🎤 HVAC STT Service Configuration
# Dual-Engine Speech-to-Text Configuration for HVAC CRM

# Service Configuration
service:
  host: "0.0.0.0"
  port: 8085
  log_level: "INFO"
  workers: 1  # Single worker for GPU usage

# Coordinator Configuration
coordinator:
  primary_engine: "nemo"  # Primary engine: nemo or elevenlabs
  fallback_enabled: true
  fallback_threshold: 0.5  # Confidence threshold for fallback
  max_retries: 2
  timeout_seconds: 30.0
  health_check_interval: 60  # seconds

# NVIDIA NeMo Configuration
nemo:
  model_path: "/app/models/stt_pl_fastconformer_hybrid_large_pc.nemo"
  device: "cuda"  # cuda or cpu
  batch_size: 1
  beam_size: 1
  return_timestamps: true
  preserve_alignment: true

# ElevenLabs Configuration
elevenlabs:
  api_key: "${ELEVENLABS_API_KEY}"  # Set via environment variable
  api_url: "https://api.elevenlabs.io/v1/speech-to-text"
  timeout: 60.0
  max_file_size: 26214400  # 25MB in bytes
  supported_formats:
    - "m4a"
    - "mp3"
    - "wav"
    - "flac"

# Audio Processing Configuration
audio:
  max_file_size: 52428800  # 50MB in bytes
  supported_formats:
    - "m4a"
    - "mp3"
    - "wav"
    - "flac"
  target_sample_rate: 16000
  normalize_audio: true
  remove_silence: false
  temp_dir: "/tmp/stt"

# Logging Configuration
logging:
  level: "INFO"
  format: "json"
  file: "/app/logs/stt-service.log"
  max_size: "100MB"
  backup_count: 5

# Metrics Configuration
metrics:
  enabled: true
  collection_interval: 30  # seconds
  retention_hours: 24
  export_prometheus: false

# Health Check Configuration
health:
  enabled: true
  interval: 60  # seconds
  timeout: 10   # seconds
  retries: 3

# Performance Configuration
performance:
  max_concurrent_requests: 5
  request_timeout: 60.0
  cleanup_interval: 300  # seconds
  temp_file_retention: 3600  # seconds

# Security Configuration
security:
  cors_enabled: true
  cors_origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
  max_request_size: 52428800  # 50MB
  rate_limiting:
    enabled: false
    requests_per_minute: 60

# Integration Configuration
integration:
  hvac_backend_url: "http://hvac-backend:8080"
  email_service_url: "http://email-intelligence:8082"
  webhook_endpoints: []

# Development Configuration
development:
  debug: false
  hot_reload: false
  mock_engines: false
  test_mode: false

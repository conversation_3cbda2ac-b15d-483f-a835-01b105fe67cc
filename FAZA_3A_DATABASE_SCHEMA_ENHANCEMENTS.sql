-- FAZA 3A: Enhanced Inventory & Financial Core - Database Schema Enhancements
-- Building on existing HVAC-Remix foundation for ERP transformation
-- Generated: January 2025

-- =====================================================
-- PURCHASE ORDER SYSTEM ENHANCEMENT
-- =====================================================

-- Enhanced Purchase Orders table
CREATE TABLE "PurchaseOrder" (
    "id" TEXT NOT NULL,
    "orderNumber" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'DRAFT', -- DRAFT, PENDING, APPROVED, ORDERED, RECEIVED, CANCELLED
    "orderDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expectedDeliveryDate" TIMESTAMP(3),
    "actualDeliveryDate" TIMESTAMP(3),
    "subtotal" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "taxAmount" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "shippingCost" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "totalAmount" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "notes" TEXT,
    "terms" TEXT,
    "deliveryAddress" TEXT,
    "urgencyLevel" TEXT NOT NULL DEFAULT 'NORMAL', -- LOW, NORMAL, HIGH, URGENT
    "approvedBy" TEXT,
    "approvedAt" TIMESTAMP(3),
    "supplierId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PurchaseOrder_pkey" PRIMARY KEY ("id")
);

-- Purchase Order Items
CREATE TABLE "PurchaseOrderItem" (
    "id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "unitPrice" DOUBLE PRECISION NOT NULL,
    "totalPrice" DOUBLE PRECISION NOT NULL,
    "receivedQuantity" INTEGER NOT NULL DEFAULT 0,
    "notes" TEXT,
    "purchaseOrderId" TEXT NOT NULL,
    "partId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PurchaseOrderItem_pkey" PRIMARY KEY ("id")
);

-- Purchase Order Approvals (for workflow)
CREATE TABLE "PurchaseOrderApproval" (
    "id" TEXT NOT NULL,
    "status" TEXT NOT NULL, -- PENDING, APPROVED, REJECTED
    "comments" TEXT,
    "approvedAt" TIMESTAMP(3),
    "purchaseOrderId" TEXT NOT NULL,
    "approverId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PurchaseOrderApproval_pkey" PRIMARY KEY ("id")
);

-- =====================================================
-- ENHANCED SUPPLIER MANAGEMENT
-- =====================================================

-- Supplier Catalog (products offered by suppliers)
CREATE TABLE "SupplierCatalog" (
    "id" TEXT NOT NULL,
    "supplierPartNumber" TEXT,
    "supplierPrice" DOUBLE PRECISION,
    "minimumOrderQuantity" INTEGER DEFAULT 1,
    "leadTimeDays" INTEGER DEFAULT 7,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "supplierId" TEXT NOT NULL,
    "partId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SupplierCatalog_pkey" PRIMARY KEY ("id")
);

-- Supplier Performance Tracking
CREATE TABLE "SupplierPerformance" (
    "id" TEXT NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "onTimeDeliveryRate" DOUBLE PRECISION DEFAULT 0,
    "qualityRating" DOUBLE PRECISION DEFAULT 0,
    "priceCompetitiveness" DOUBLE PRECISION DEFAULT 0,
    "responseTime" DOUBLE PRECISION DEFAULT 0,
    "totalOrders" INTEGER DEFAULT 0,
    "totalValue" DOUBLE PRECISION DEFAULT 0,
    "supplierId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SupplierPerformance_pkey" PRIMARY KEY ("id")
);

-- =====================================================
-- ENHANCED FINANCIAL MANAGEMENT
-- =====================================================

-- Chart of Accounts
CREATE TABLE "ChartOfAccounts" (
    "id" TEXT NOT NULL,
    "accountNumber" TEXT NOT NULL,
    "accountName" TEXT NOT NULL,
    "accountType" TEXT NOT NULL, -- ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE
    "parentAccountId" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ChartOfAccounts_pkey" PRIMARY KEY ("id")
);

-- Journal Entries for double-entry bookkeeping
CREATE TABLE "JournalEntry" (
    "id" TEXT NOT NULL,
    "entryNumber" TEXT NOT NULL,
    "entryDate" TIMESTAMP(3) NOT NULL,
    "description" TEXT NOT NULL,
    "reference" TEXT,
    "totalDebit" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "totalCredit" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "status" TEXT NOT NULL DEFAULT 'DRAFT', -- DRAFT, POSTED, REVERSED
    "createdById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "JournalEntry_pkey" PRIMARY KEY ("id")
);

-- Journal Entry Lines
CREATE TABLE "JournalEntryLine" (
    "id" TEXT NOT NULL,
    "debitAmount" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "creditAmount" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "description" TEXT,
    "journalEntryId" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "JournalEntryLine_pkey" PRIMARY KEY ("id")
);

-- Enhanced Payment Terms
CREATE TABLE "PaymentTerms" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "netDays" INTEGER NOT NULL DEFAULT 30,
    "discountDays" INTEGER DEFAULT 0,
    "discountPercentage" DOUBLE PRECISION DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PaymentTerms_pkey" PRIMARY KEY ("id")
);

-- =====================================================
-- ENHANCED INVENTORY MANAGEMENT
-- =====================================================

-- Inventory Adjustments (for stock corrections)
CREATE TABLE "InventoryAdjustment" (
    "id" TEXT NOT NULL,
    "adjustmentNumber" TEXT NOT NULL,
    "adjustmentDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "reason" TEXT NOT NULL,
    "notes" TEXT,
    "status" TEXT NOT NULL DEFAULT 'PENDING', -- PENDING, APPROVED, COMPLETED
    "totalValue" DOUBLE PRECISION DEFAULT 0,
    "approvedById" TEXT,
    "approvedAt" TIMESTAMP(3),
    "createdById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InventoryAdjustment_pkey" PRIMARY KEY ("id")
);

-- Inventory Adjustment Items
CREATE TABLE "InventoryAdjustmentItem" (
    "id" TEXT NOT NULL,
    "currentQuantity" INTEGER NOT NULL,
    "adjustedQuantity" INTEGER NOT NULL,
    "quantityDifference" INTEGER NOT NULL,
    "unitCost" DOUBLE PRECISION,
    "totalValue" DOUBLE PRECISION,
    "reason" TEXT,
    "adjustmentId" TEXT NOT NULL,
    "partId" TEXT NOT NULL,
    "locationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "InventoryAdjustmentItem_pkey" PRIMARY KEY ("id")
);

-- Inventory Valuation (for cost tracking)
CREATE TABLE "InventoryValuation" (
    "id" TEXT NOT NULL,
    "valuationDate" TIMESTAMP(3) NOT NULL,
    "method" TEXT NOT NULL DEFAULT 'FIFO', -- FIFO, LIFO, AVERAGE
    "quantity" INTEGER NOT NULL,
    "unitCost" DOUBLE PRECISION NOT NULL,
    "totalValue" DOUBLE PRECISION NOT NULL,
    "partId" TEXT NOT NULL,
    "locationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "InventoryValuation_pkey" PRIMARY KEY ("id")
);

-- =====================================================
-- COST CENTER & PROJECT COSTING
-- =====================================================

-- Cost Centers for expense allocation
CREATE TABLE "CostCenter" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "parentCostCenterId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CostCenter_pkey" PRIMARY KEY ("id")
);

-- Project Cost Tracking
CREATE TABLE "ProjectCost" (
    "id" TEXT NOT NULL,
    "costDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "costType" TEXT NOT NULL, -- LABOR, MATERIAL, OVERHEAD, EQUIPMENT
    "amount" DOUBLE PRECISION NOT NULL,
    "description" TEXT,
    "reference" TEXT,
    "projectId" TEXT NOT NULL,
    "costCenterId" TEXT,
    "serviceOrderId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProjectCost_pkey" PRIMARY KEY ("id")
);

-- =====================================================
-- FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Purchase Order constraints
ALTER TABLE "PurchaseOrder" ADD CONSTRAINT "PurchaseOrder_supplierId_fkey" 
    FOREIGN KEY ("supplierId") REFERENCES "Supplier"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "PurchaseOrder" ADD CONSTRAINT "PurchaseOrder_createdById_fkey" 
    FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "PurchaseOrderItem" ADD CONSTRAINT "PurchaseOrderItem_purchaseOrderId_fkey" 
    FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "PurchaseOrderItem" ADD CONSTRAINT "PurchaseOrderItem_partId_fkey" 
    FOREIGN KEY ("partId") REFERENCES "InventoryPart"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Supplier constraints
ALTER TABLE "SupplierCatalog" ADD CONSTRAINT "SupplierCatalog_supplierId_fkey" 
    FOREIGN KEY ("supplierId") REFERENCES "Supplier"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "SupplierCatalog" ADD CONSTRAINT "SupplierCatalog_partId_fkey" 
    FOREIGN KEY ("partId") REFERENCES "InventoryPart"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Financial constraints
ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_createdById_fkey" 
    FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "JournalEntryLine" ADD CONSTRAINT "JournalEntryLine_journalEntryId_fkey" 
    FOREIGN KEY ("journalEntryId") REFERENCES "JournalEntry"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "JournalEntryLine" ADD CONSTRAINT "JournalEntryLine_accountId_fkey" 
    FOREIGN KEY ("accountId") REFERENCES "ChartOfAccounts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Project costing constraints
ALTER TABLE "ProjectCost" ADD CONSTRAINT "ProjectCost_projectId_fkey" 
    FOREIGN KEY ("projectId") REFERENCES "InstallationProject"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "ProjectCost" ADD CONSTRAINT "ProjectCost_serviceOrderId_fkey" 
    FOREIGN KEY ("serviceOrderId") REFERENCES "ServiceOrder"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

CREATE INDEX "PurchaseOrder_orderNumber_idx" ON "PurchaseOrder"("orderNumber");
CREATE INDEX "PurchaseOrder_status_idx" ON "PurchaseOrder"("status");
CREATE INDEX "PurchaseOrder_supplierId_idx" ON "PurchaseOrder"("supplierId");
CREATE INDEX "SupplierCatalog_supplierId_partId_idx" ON "SupplierCatalog"("supplierId", "partId");
CREATE INDEX "JournalEntry_entryDate_idx" ON "JournalEntry"("entryDate");
CREATE INDEX "ProjectCost_projectId_costDate_idx" ON "ProjectCost"("projectId", "costDate");

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Default Payment Terms
INSERT INTO "PaymentTerms" ("id", "name", "description", "netDays") VALUES
('pt_net30', 'Net 30', 'Payment due within 30 days', 30),
('pt_net15', 'Net 15', 'Payment due within 15 days', 15),
('pt_cod', 'COD', 'Cash on delivery', 0),
('pt_2_10_net30', '2/10 Net 30', '2% discount if paid within 10 days, otherwise net 30', 30);

-- Default Chart of Accounts (basic HVAC structure)
INSERT INTO "ChartOfAccounts" ("id", "accountNumber", "accountName", "accountType") VALUES
('acc_1000', '1000', 'Cash', 'ASSET'),
('acc_1200', '1200', 'Accounts Receivable', 'ASSET'),
('acc_1300', '1300', 'Inventory', 'ASSET'),
('acc_2000', '2000', 'Accounts Payable', 'LIABILITY'),
('acc_3000', '3000', 'Owner Equity', 'EQUITY'),
('acc_4000', '4000', 'Service Revenue', 'REVENUE'),
('acc_5000', '5000', 'Cost of Goods Sold', 'EXPENSE'),
('acc_6000', '6000', 'Operating Expenses', 'EXPENSE');

-- Default Cost Centers
INSERT INTO "CostCenter" ("id", "code", "name", "description") VALUES
('cc_install', 'INSTALL', 'Installation Services', 'HVAC installation projects'),
('cc_service', 'SERVICE', 'Service & Maintenance', 'Ongoing service and maintenance'),
('cc_admin', 'ADMIN', 'Administration', 'Administrative overhead'),
('cc_sales', 'SALES', 'Sales & Marketing', 'Sales and marketing activities');
